{"extends": ["config:best-practices"], "labels": ["renovate"], "schedule": ["* 0-4 * * 4"], "prHourlyLimit": 10, "prConcurrentLimit": 10, "packageRules": [{"groupName": "Update composer packages to non-major versions", "matchPackageNames": ["*"], "matchUpdateTypes": ["minor", "patch"], "matchManagers": ["composer"]}, {"groupName": "Update local docker-compose env dependencies", "matchPackageNames": ["*", "!ghcr.io/prezero/**"], "matchManagers": ["docker-compose"]}, {"groupName": "Update prezero docker images", "matchPackageNames": ["ghcr.io/prezero/**"], "matchUpdateTypes": ["minor", "patch", "major"]}, {"groupName": "Update github actions", "matchPackageNames": ["*", "!ghcr.io/prezero/**"], "matchManagers": ["github-actions"]}, {"matchPackageNames": ["ghcr.io/prezero/**"], "matchUpdateTypes": ["pinDigest", "digest"], "enabled": false}]}