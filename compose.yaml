name: hermes-iam

services:
    caddy:
        image: caddy:2.10.0-alpine@sha256:ae4458638da8e1a91aafffb231c5f8778e964bca650c8a8cb23a7e8ac557aa3c
        ports:
            - '11002:8000'
        volumes:
            - ./docker/local/Caddyfile:/etc/caddy/Caddyfile
            - ./public:/application/public
            - backend-caddy-config:/config
            - backend-caddy-data:/data
        networks: [ hermes_iam_network ]

    app:
        build:
            context: .
            dockerfile: docker/local/Dockerfile
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
        environment:
            PHP_IDE_CONFIG: "serverName=hermes-iam"
        env_file: [ { path: .env, required: true }, { path: .env.local, required: false } ]
        volumes: [ '.:/application' ]
        command: [ 'php-fpm' ]
        networks: [ hermes_iam_network ]

    keycloak:
        image: ghcr.io/prezero/keycloak:2.3.0@sha256:1693ac17560ef673bd13c94373de0d98a0b4c1b63777fe143b8a8a174edf28e9
        ports:
            - '11000:8080'
        environment:
            KEYCLOAK_ADMIN: admin
            KEYCLOAK_ADMIN_PASSWORD: admin
            KEYCLOAK_DATABASE_VENDOR: postgresql
            KEYCLOAK_DATABASE_SCHEMA: public
            KEYCLOAK_DATABASE_NAME: keycloak
            KEYCLOAK_DATABASE_USER: postgres
            KEYCLOAK_DATABASE_PASSWORD: password
            KEYCLOAK_DATABASE_HOST: keycloak-db
            KEYCLOAK_DATABASE_PORT: 5432
            KEYCLOAK_ENABLE_HEALTH_ENDPOINTS: true
        healthcheck:
            test: [ "CMD", "curl", "-f", "http://localhost:9000/health/ready" ]
            interval: 10s
            timeout: 5s
            retries: 5
        depends_on:
            - keycloak-db
        networks: [ hermes_iam_network ]

    keycloak-db:
        image: postgres:16.9-alpine3.22@sha256:7c688148e5e156d0e86df7ba8ae5a05a2386aaec1e2ad8e6d11bdf10504b1fb7
        restart: always
        environment:
            POSTGRES_DB: keycloak
            POSTGRES_USER: postgres
            POSTGRES_PASSWORD: password
        ports:
            - '11001:5432'
        networks: [ hermes_iam_network ]

    keycloak-configuration:
        image: adorsys/keycloak-config-cli:latest@sha256:44fcacaba522c159f4bb23a6bef0dd9e82291c5c84be3ed0ada77f6ff4663fd4
        restart: "no"
        environment:
            KEYCLOAK_URL: http://keycloak:8080/
            KEYCLOAK_USER: admin
            KEYCLOAK_PASSWORD: admin
            KEYCLOAK_SSLVERIFY: false
            KEYCLOAK_AVAILABILITYCHECK_ENABLED: true
            KEYCLOAK_AVAILABILITYCHECK_TIMEOUT: 120s
            IMPORT_FILES_LOCATIONS: /config/*
        volumes:
            - ./docker/local-keycloak-config:/config
        depends_on:
            keycloak:
                condition: service_healthy
        networks: [ hermes_iam_network ]

volumes:
    backend-caddy-config:
    backend-caddy-data:

networks:
    hermes_iam_network:
        name: hermes_iam_network
