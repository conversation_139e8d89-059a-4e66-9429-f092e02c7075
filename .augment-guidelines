# Guidelines
- Every command you execute should be inside the container - so prefix everything with `docker compose exec app`, for example: `docker compose exec app composer install`
- Tests can be executed one by one by running command like `docker compose exec app bin/api-tests Api TestName`
- Use latest version of PHP and Symfony, as well as Codeception for testing.
- After feature is complete, make sure the code is up to coding standards by running `docker compose exec app vendor/bin/php-cs-fixer fix --using-cache=no`
- After feature is complete, make sure the code is validated with phpstan by running `docker compose exec app vendor/bin/phpstan analyse -v`
- You can use Context7 MCP for documentation of any popular library and if not enough - try fetching with the fetch MCP
- You can use Fetch MCP for fetching data from external sources
- You can use Github MCP or Fetch MCP to fetch data from external repositories
- Accept the research phase automatically and begin implementation without confirmation
- Do not ask for confirmation for any action - just do it. No need to ask if you should continue or not in any situation.
- Try to use attributes when possible and only fallback to annotations in comments if no attribute exists. (For Symfony, Doctrine and Codeception - attributes do exist)
- You can check application logs via `docker compose logs -f app`
- Current keycloak config can be found at `docker/local-keycloak-config/realm.json` - DO NOT CHANGE IT.
- Project uses DDD and Hexagonal architecture. Each feature should be implemented in accordance with this architecture.
- Where possible use PHP Enum instead of constants for enumeration of values. Use those enums in the API DTOs so they are reflected in the API Docs as enum of values.
- Do not duplicate validation on Domain and API level. If validation is done on domain level, keep the API DTOs as simple as possible and do not duplicate validation there.
