{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "fc151e8d7ab6fb59e21b1276643c023a", "packages": [{"name": "brick/math", "version": "0.13.1", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/fc7ed316430118cc7836bf45faff18d5dfc8de04", "reference": "fc7ed316430118cc7836bf45faff18d5dfc8de04", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^10.1", "vimeo/psalm": "6.8.8"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "bignumber", "brick", "decimal", "integer", "math", "mathematics", "rational"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.13.1"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2025-03-29T13:50:30+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.5", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "reference": "459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"phpunit/phpunit": "<=7.5 || >=13"}, "require-dev": {"doctrine/coding-standard": "^9 || ^12 || ^13", "phpstan/phpstan": "1.4.10 || 2.1.11", "phpstan/phpstan-phpunit": "^1.0 || ^2", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.6 || ^10.5 || ^11.5 || ^12", "psr/log": "^1 || ^2 || ^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.5"}, "time": "2025-04-07T20:06:18+00:00"}, {"name": "enlightn/security-checker", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/enlightn/security-checker.git", "reference": "68df5c7256c84b428bf8fcff0d249de06ce362d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/enlightn/security-checker/zipball/68df5c7256c84b428bf8fcff0d249de06ce362d2", "reference": "68df5c7256c84b428bf8fcff0d249de06ce362d2", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.6", "symfony/console": "^3.4|^4|^5|^6|^7", "symfony/finder": "^3|^4|^5|^6|^7", "symfony/process": "^3.4|^4|^5|^6|^7", "symfony/yaml": "^3.4|^4|^5|^6|^7"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^2.18|^3.0", "phpunit/phpunit": "^5.5|^6|^7|^8|^9"}, "bin": ["security-checker"], "type": "library", "autoload": {"psr-4": {"Enlightn\\SecurityChecker\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP dependency vulnerabilities scanner based on the Security Advisories Database.", "keywords": ["package", "php", "scanner", "security", "security advisories", "vulnerability scanner"], "support": {"issues": "https://github.com/enlightn/security-checker/issues", "source": "https://github.com/enlightn/security-checker/tree/v1.11.0"}, "time": "2023-11-17T07:53:29+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.9.3", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "reference": "7b2f29fe81dc4da0ca0ea7d42107a0845946ea77", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.3", "guzzlehttp/psr7": "^2.7.0", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "guzzle/client-integration-tests": "3.0.2", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.39 || ^9.6.20", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.9.3"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2025-03-27T13:37:11+00:00"}, {"name": "guzzlehttp/promises", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/7c69f28996b0a6920945dd20b3857e499d9ca96c", "reference": "7c69f28996b0a6920945dd20b3857e499d9ca96c", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.2.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2025-03-27T13:27:01+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.7.1", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/c2270caaabe631b3b44c85f99e5a04bbb8060d16", "reference": "c2270caaabe631b3b44c85f99e5a04bbb8060d16", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "0.9.0", "phpunit/phpunit": "^8.5.39 || ^9.6.20"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.7.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2025-03-27T12:30:47+00:00"}, {"name": "laminas/laminas-diagnostics", "version": "1.27.0", "source": {"type": "git", "url": "https://github.com/laminas/laminas-diagnostics.git", "reference": "a9910eb2e3a503a476708eb611cb9a0318ffc8c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laminas/laminas-diagnostics/zipball/a9910eb2e3a503a476708eb611cb9a0318ffc8c6", "reference": "a9910eb2e3a503a476708eb611cb9a0318ffc8c6", "shasum": ""}, "require": {"enlightn/security-checker": "^1.10", "php": "~8.1.0 || ~8.2.0 || ~8.3.0 || ~8.4.0"}, "conflict": {"guzzlehttp/promises": "<2.0.4", "guzzlehttp/ringphp": "<1.1.1", "symfony/finder": "<5.3.7", "symfony/process": "<5.4.46", "zendframework/zenddiagnostics": "*"}, "require-dev": {"doctrine/migrations": "^2.0 || ^3.8.2", "guzzlehttp/guzzle": "^7.9.2", "laminas/laminas-coding-standard": "~2.5.0", "laminas/laminas-loader": "^2.11", "mikey179/vfsstream": "^1.6.12", "php-amqplib/php-amqplib": "^2.0 || ^3.7.2", "phpunit/phpunit": "^9.6.21", "predis/predis": "^2.3.0", "psalm/plugin-phpunit": "^0.18.4", "symfony/yaml": "^6.4.13 || ^7.0.0", "vimeo/psalm": "^4.30.0"}, "suggest": {"doctrine/migrations": "Required by Check\\DoctrineMigration", "ext-bcmath": "Required by Check\\CpuPerformance", "guzzlehttp/guzzle": "Required by Check\\GuzzleHttpService", "php-amqplib/php-amqplib": "Required by Check\\RabbitMQ", "predis/predis": "Required by Check\\Redis", "symfony/yaml": "Required by Check\\YamlFile"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev", "dev-develop": "1.7.x-dev"}}, "autoload": {"files": ["src/autoload.php"], "psr-4": {"Laminas\\Diagnostics\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "A set of components for performing diagnostic tests in PHP applications", "homepage": "https://laminas.dev", "keywords": ["diagnostics", "laminas", "php", "test"], "support": {"chat": "https://laminas.dev/chat", "docs": "https://docs.laminas.dev/laminas-diagnostics/", "forum": "https://discourse.laminas.dev", "issues": "https://github.com/laminas/laminas-diagnostics/issues", "rss": "https://github.com/laminas/laminas-diagnostics/releases.atom", "source": "https://github.com/laminas/laminas-diagnostics"}, "funding": [{"url": "https://funding.communitybridge.org/projects/laminas-project", "type": "community_bridge"}], "time": "2024-12-17T16:35:50+00:00"}, {"name": "liip/monitor-bundle", "version": "2.24.0", "source": {"type": "git", "url": "https://github.com/liip/LiipMonitorBundle.git", "reference": "833ca254ff9d783ea3b5f30b61ea43a1aa6758df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/liip/LiipMonitorBundle/zipball/833ca254ff9d783ea3b5f30b61ea43a1aa6758df", "reference": "833ca254ff9d783ea3b5f30b61ea43a1aa6758df", "shasum": ""}, "require": {"laminas/laminas-diagnostics": "^1.9", "php": "^8.1", "symfony/framework-bundle": "^6.4|^7.0"}, "require-dev": {"doctrine/doctrine-migrations-bundle": "^2.0 || ^3.0 || ^7.0", "doctrine/migrations": "^2.0 || ^3.0", "doctrine/persistence": "^1.3.3 || ^2.0 || ^3.0", "enlightn/security-checker": "^1.11", "guzzlehttp/guzzle": "^5.3.2 || ^6.3.3 || ^7.0.1", "matthiasnoback/symfony-dependency-injection-test": "^4.3 || ^5.0", "php-cs-fixer/shim": "^3.75", "phpunit/phpunit": "^9.6.23", "symfony/asset": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/phpunit-bridge": "^7.3", "symfony/twig-bundle": "^6.4|^7.0"}, "suggest": {"symfony/expression-language": "To use the Expression check"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Liip\\MonitorBundle\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://zenstruck.com/"}, {"name": "Liip AG", "homepage": "https://www.liip.ch/"}, {"name": "Symfony Community", "homepage": "https://github.com/liip/LiipMonitorBundle/contributors"}], "description": "Liip Monitor Bundle", "keywords": ["check", "health", "monitor", "monitoring"], "support": {"issues": "https://github.com/liip/LiipMonitorBundle/issues", "source": "https://github.com/liip/LiipMonitorBundle/tree/2.24.0"}, "time": "2025-06-25T15:34:32+00:00"}, {"name": "monolog/monolog", "version": "3.9.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/10d85740180ecba7896c87e06a166e0c95a0e3b6", "reference": "10d85740180ecba7896c87e06a166e0c95a0e3b6", "shasum": ""}, "require": {"php": ">=8.1", "psr/log": "^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7 || ^8", "ext-json": "*", "graylog2/gelf-php": "^1.4.2 || ^2.0", "guzzlehttp/guzzle": "^7.4.5", "guzzlehttp/psr7": "^2.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.8", "phpstan/phpstan": "^2", "phpstan/phpstan-deprecation-rules": "^2", "phpstan/phpstan-strict-rules": "^2", "phpunit/phpunit": "^10.5.17 || ^11.0.7", "predis/predis": "^1.1 || ^2", "rollbar/rollbar": "^4.0", "ruflin/elastica": "^7 || ^8", "symfony/mailer": "^5.4 || ^6", "symfony/mime": "^5.4 || ^6"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/3.9.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2025-03-24T10:02:05+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.6.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/92dde6a5919e34835c506ac8c523ef095a95ed62", "reference": "92dde6a5919e34835c506ac8c523ef095a95ed62", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7|^2.0", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5 || ~1.6.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "psalm/phar": "^5.26"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.6.2"}, "time": "2025-04-13T19:20:35+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/679e3ce485b99e84c775d28e2e96fade9a7fb50a", "reference": "679e3ce485b99e84c775d28e2e96fade9a7fb50a", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.18|^2.0"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.10.0"}, "time": "2024-11-09T15:12:26+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "b9e61a61e39e02dd90944e9115241c7f7e76bfd8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/b9e61a61e39e02dd90944e9115241c7f7e76bfd8", "reference": "b9e61a61e39e02dd90944e9115241c7f7e76bfd8", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/2.2.0"}, "time": "2025-07-13T07:04:09+00:00"}, {"name": "prezero/api-bundle", "version": "v1.12.0", "source": {"type": "git", "url": "**************:prezero/api-bundle.git", "reference": "66cf536108ae6ef5c639ec8f63d71c19e82cb3a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/prezero/api-bundle/zipball/66cf536108ae6ef5c639ec8f63d71c19e82cb3a2", "reference": "66cf536108ae6ef5c639ec8f63d71c19e82cb3a2", "shasum": ""}, "require": {"php": "^8.4", "symfony/console": "^7.2", "symfony/expression-language": "^7.2", "symfony/framework-bundle": "^7.2", "symfony/http-kernel": "^7.2", "symfony/routing": "^7.2", "symfony/security-core": "^7.2", "symfony/serializer": "^7.2", "symfony/validator": "^7.2", "vuryss/dokky": "^1.6.0", "vuryss/serializer": "^1.9"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/hook-installer": "^1.0", "friendsofphp/php-cs-fixer": "^3.73", "phpstan/phpstan": "^2.1", "phpunit/phpunit": "^12.0", "ramsey/conventional-commits": "^1.6"}, "type": "library", "autoload": {"psr-4": {"PreZero\\ApiBundle\\": "src/"}}, "autoload-dev": {"psr-4": {"PreZero\\ApiBundle\\Tests\\": "tests/"}}, "scripts": {"test": ["phpunit"], "test-coverage": ["XDEBUG_MODE=coverage phpunit --coverage-html coverage"]}, "license": ["proprietary"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Opinionated API Bundle", "support": {"source": "https://github.com/prezero/api-bundle/tree/v1.12.0", "issues": "https://github.com/prezero/api-bundle/issues"}, "time": "2025-07-01T08:38:56+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "spomky-labs/pki-framework", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/Spomky-Labs/pki-framework.git", "reference": "eced5b5ce70518b983ff2be486e902bbd15135ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Spomky-Labs/pki-framework/zipball/eced5b5ce70518b983ff2be486e902bbd15135ae", "reference": "eced5b5ce70518b983ff2be486e902bbd15135ae", "shasum": ""}, "require": {"brick/math": "^0.10|^0.11|^0.12|^0.13", "ext-mbstring": "*", "php": ">=8.1"}, "require-dev": {"ekino/phpstan-banned-code": "^1.0|^2.0|^3.0", "ext-gmp": "*", "ext-openssl": "*", "infection/infection": "^0.28|^0.29", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.3|^2.0", "phpstan/phpstan": "^1.8|^2.0", "phpstan/phpstan-deprecation-rules": "^1.0|^2.0", "phpstan/phpstan-phpunit": "^1.1|^2.0", "phpstan/phpstan-strict-rules": "^1.3|^2.0", "phpunit/phpunit": "^10.1|^11.0|^12.0", "rector/rector": "^1.0|^2.0", "roave/security-advisories": "dev-latest", "symfony/string": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symplify/easy-coding-standard": "^12.0"}, "suggest": {"ext-bcmath": "For better performance (or GMP)", "ext-gmp": "For better performance (or BCMath)", "ext-openssl": "For OpenSSL based cyphering"}, "type": "library", "autoload": {"psr-4": {"SpomkyLabs\\Pki\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Original developer"}, {"name": "Florent <PERSON>", "email": "<EMAIL>", "role": "Spomky-Labs PKI Framework developer"}], "description": "A PHP framework for managing Public Key Infrastructures. It comprises X.509 public key certificates, attribute certificates, certification requests and certification path validation.", "homepage": "https://github.com/spomky-labs/pki-framework", "keywords": ["DER", "Private Key", "ac", "algorithm identifier", "asn.1", "asn1", "attribute certificate", "certificate", "certification request", "cryptography", "csr", "decrypt", "ec", "encrypt", "pem", "pkcs", "public key", "rsa", "sign", "signature", "verify", "x.509", "x.690", "x509", "x690"], "support": {"issues": "https://github.com/Spomky-Labs/pki-framework/issues", "source": "https://github.com/Spomky-Labs/pki-framework/tree/1.3.0"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2025-06-13T08:35:04+00:00"}, {"name": "symfony/asset", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "56c4d9f759247c4e07d8549e3baf7493cb9c3e4b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/56c4d9f759247c4e07d8549e3baf7493cb9c3e4b", "reference": "56c4d9f759247c4e07d8549e3baf7493cb9c3e4b", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-05T10:15:41+00:00"}, {"name": "symfony/cache", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e", "reference": "a7c6caa9d6113cebfb3020b427bcb021ebfdfc9e", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^3.6", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/5d68a57d66910405e5c0b63d6f0af941e66fc868", "reference": "5d68a57d66910405e5c0b63d6f0af941e66fc868", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-13T15:25:07+00:00"}, {"name": "symfony/clock", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "reference": "b81435fbd6648ea425d1ee96a2d8e68f4ceacd24", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/config", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/ba62ae565f1327c2f6366726312ed828c85853bc", "reference": "ba62ae565f1327c2f6366726312ed828c85853bc", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/console", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/9e27aecde8f506ba0fd1d9989620c04a87697101", "reference": "9e27aecde8f506ba0fd1d9989620c04a87697101", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^7.2"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "8656c4848b48784c4bb8c4ae50d2b43f832cead8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/8656c4848b48784c4bb8c4ae50d2b43f832cead8", "reference": "8656c4848b48784c4bb8c4ae50d2b43f832cead8", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4.20|^7.2.5"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T04:04:43+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/63afe740e99a13ba87ec199bb07bbdee937a5b62", "reference": "63afe740e99a13ba87ec199bb07bbdee937a5b62", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/dotenv", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/dotenv.git", "reference": "28347a897771d0c28e99b75166dd2689099f3045"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dotenv/zipball/28347a897771d0c28e99b75166dd2689099f3045", "reference": "28347a897771d0c28e99b75166dd2689099f3045", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/console": "<6.4", "symfony/process": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Dotenv\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Registers environment variables from a .env file", "homepage": "https://symfony.com", "keywords": ["dotenv", "env", "environment"], "support": {"source": "https://github.com/symfony/dotenv/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-27T11:18:42+00:00"}, {"name": "symfony/error-handler", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "35b55b166f6752d6aaf21aa042fc5ed280fce235"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/35b55b166f6752d6aaf21aa042fc5ed280fce235", "reference": "35b55b166f6752d6aaf21aa042fc5ed280fce235", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/var-dumper": "^6.4|^7.0"}, "conflict": {"symfony/deprecation-contracts": "<2.5", "symfony/http-kernel": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-kernel": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/webpack-encore-bundle": "^1.0|^2.0"}, "bin": ["Resources/bin/patch-type-declarations"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-13T07:48:40+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/497f73ac996a598c92409b44ac43b6690c4f666d", "reference": "497f73ac996a598c92409b44ac43b6690c4f666d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-22T09:11:45+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "59eb412e93815df44f05f342958efa9f46b1e586"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/59eb412e93815df44f05f342958efa9f46b1e586", "reference": "59eb412e93815df44f05f342958efa9f46b1e586", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/expression-language", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "26f4884a455e755e630a5fc372df124a3578da2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/26f4884a455e755e630a5fc372df124a3578da2e", "reference": "26f4884a455e755e630a5fc372df124a3578da2e", "shasum": ""}, "require": {"php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-15T11:52:45+00:00"}, {"name": "symfony/filesystem", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "reference": "b8dce482de9d7c9fe2891155035a7248ab5c7fdb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-10-25T15:15:23+00:00"}, {"name": "symfony/finder", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/ec2344cf77a48253bbca6939aa3d2477773ea63d", "reference": "ec2344cf77a48253bbca6939aa3d2477773ea63d", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-30T19:00:26+00:00"}, {"name": "symfony/flex", "version": "v2.8.1", "source": {"type": "git", "url": "https://github.com/symfony/flex.git", "reference": "423c36e369361003dc31ef11c5f15fb589e52c01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/flex/zipball/423c36e369361003dc31ef11c5f15fb589e52c01", "reference": "423c36e369361003dc31ef11c5f15fb589e52c01", "shasum": ""}, "require": {"composer-plugin-api": "^2.1", "php": ">=8.0"}, "conflict": {"composer/semver": "<1.7.2"}, "require-dev": {"composer/composer": "^2.1", "symfony/dotenv": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/phpunit-bridge": "^5.4|^6.0", "symfony/process": "^5.4|^6.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Flex\\Flex"}, "autoload": {"psr-4": {"Symfony\\Flex\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Composer plugin for Symfony", "support": {"issues": "https://github.com/symfony/flex/issues", "source": "https://github.com/symfony/flex/tree/v2.8.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-07-05T07:45:19+00:00"}, {"name": "symfony/framework-bundle", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/framework-bundle.git", "reference": "91905f22f26aa350a33b3b9690bdf94976b0d0ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/framework-bundle/zipball/91905f22f26aa350a33b3b9690bdf94976b0d0ab", "reference": "91905f22f26aa350a33b3b9690bdf94976b0d0ab", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/config": "^7.3", "symfony/dependency-injection": "^7.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^7.3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/filesystem": "^7.1", "symfony/finder": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^7.2", "symfony/polyfill-mbstring": "~1.0", "symfony/routing": "^6.4|^7.0"}, "conflict": {"doctrine/persistence": "<1.3", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/asset": "<6.4", "symfony/asset-mapper": "<6.4", "symfony/clock": "<6.4", "symfony/console": "<6.4", "symfony/dom-crawler": "<6.4", "symfony/dotenv": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/json-streamer": ">=7.4", "symfony/lock": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/object-mapper": ">=7.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/runtime": "<6.4.13|>=7.0,<7.1.6", "symfony/scheduler": "<6.4.4|>=7.0.0,<7.0.4", "symfony/security-core": "<6.4", "symfony/security-csrf": "<7.2", "symfony/serializer": "<7.2.5", "symfony/stopwatch": "<6.4", "symfony/translation": "<7.3", "symfony/twig-bridge": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4", "symfony/web-profiler-bundle": "<6.4", "symfony/webhook": "<7.2", "symfony/workflow": "<7.3.0-beta2"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "dragonmantank/cron-expression": "^3.1", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "seld/jsonlint": "^1.10", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/json-streamer": "7.3.*", "symfony/lock": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/notifier": "^6.4|^7.0", "symfony/object-mapper": "^v7.3.0-beta2", "symfony/polyfill-intl-icu": "~1.0", "symfony/process": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/scheduler": "^6.4.4|^7.0.4", "symfony/security-bundle": "^6.4|^7.0", "symfony/semaphore": "^6.4|^7.0", "symfony/serializer": "^7.2.5", "symfony/stopwatch": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^7.3", "symfony/twig-bundle": "^6.4|^7.0", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/webhook": "^7.2", "symfony/workflow": "^7.3", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\FrameworkBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration between Symfony components and the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/framework-bundle/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/http-client", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "4403d87a2c16f33345dca93407a8714ee8c05a64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/4403d87a2c16f33345dca93407a8714ee8c05a64", "reference": "4403d87a2c16f33345dca93407a8714ee8c05a64", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-client-contracts": "~3.4.4|^3.5.2", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"amphp/amp": "<2.5", "amphp/socket": "<1.1", "php-http/discovery": "<1.15", "symfony/http-foundation": "<6.4"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/http-client": "^4.2.1|^5.0", "amphp/http-tunnel": "^1.0|^2.0", "guzzlehttp/promises": "^1.4|^2.0", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/amphp-http-client-meta": "^1.0|^2.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "keywords": ["http"], "support": {"source": "https://github.com/symfony/http-client/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-28T07:58:39+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "75d7043853a42837e68111812f4d964b01e5101c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/75d7043853a42837e68111812f4d964b01e5101c", "reference": "75d7043853a42837e68111812f4d964b01e5101c", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-29T11:18:49+00:00"}, {"name": "symfony/http-foundation", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "23dd60256610c86a3414575b70c596e5deff6ed9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/23dd60256610c86a3414575b70c596e5deff6ed9", "reference": "23dd60256610c86a3414575b70c596e5deff6ed9", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4.12|>=7.0,<7.1.5"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4.12|^7.1.5", "symfony/clock": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-23T15:07:14+00:00"}, {"name": "symfony/http-kernel", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "1644879a66e4aa29c36fe33dfa6c54b450ce1831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/1644879a66e4aa29c36fe33dfa6c54b450ce1831", "reference": "1644879a66e4aa29c36fe33dfa6c54b450ce1831", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/error-handler": "^6.4|^7.0", "symfony/event-dispatcher": "^7.3", "symfony/http-foundation": "^7.3", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/cache": "<6.4", "symfony/config": "<6.4", "symfony/console": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<6.4", "symfony/form": "<6.4", "symfony/http-client": "<6.4", "symfony/http-client-contracts": "<2.5", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/translation": "<6.4", "symfony/translation-contracts": "<2.5", "symfony/twig-bridge": "<6.4", "symfony/validator": "<6.4", "symfony/var-dumper": "<6.4", "twig/twig": "<3.12"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client-contracts": "^2.5|^3", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^7.1", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^7.1", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "twig/twig": "^3.12"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-28T08:24:55+00:00"}, {"name": "symfony/monolog-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bridge.git", "reference": "1b188c8abbbef25b111da878797514b7a8d33990"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bridge/zipball/1b188c8abbbef25b111da878797514b7a8d33990", "reference": "1b188c8abbbef25b111da878797514b7a8d33990", "shasum": ""}, "require": {"monolog/monolog": "^3", "php": ">=8.2", "symfony/http-kernel": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/console": "<6.4", "symfony/http-foundation": "<6.4", "symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mailer": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Monolog\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Monolog with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/monolog-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-21T12:17:46+00:00"}, {"name": "symfony/monolog-bundle", "version": "v3.10.0", "source": {"type": "git", "url": "https://github.com/symfony/monolog-bundle.git", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/monolog-bundle/zipball/414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "reference": "414f951743f4aa1fd0f5bf6a0e9c16af3fe7f181", "shasum": ""}, "require": {"monolog/monolog": "^1.25.1 || ^2.0 || ^3.0", "php": ">=7.2.5", "symfony/config": "^5.4 || ^6.0 || ^7.0", "symfony/dependency-injection": "^5.4 || ^6.0 || ^7.0", "symfony/http-kernel": "^5.4 || ^6.0 || ^7.0", "symfony/monolog-bridge": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/phpunit-bridge": "^6.3 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "type": "symfony-bundle", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Symfony\\Bundle\\MonologBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony MonologBundle", "homepage": "https://symfony.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/symfony/monolog-bundle/issues", "source": "https://github.com/symfony/monolog-bundle/tree/v3.10.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-11-06T17:08:13+00:00"}, {"name": "symfony/password-hasher", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/password-hasher.git", "reference": "31fbe66af859582a20b803f38be96be8accdf2c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/password-hasher/zipball/31fbe66af859582a20b803f38be96be8accdf2c3", "reference": "31fbe66af859582a20b803f38be96be8accdf2c3", "shasum": ""}, "require": {"php": ">=8.2"}, "conflict": {"symfony/security-core": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/security-core": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PasswordHasher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides password hashing utilities", "homepage": "https://symfony.com", "keywords": ["hashing", "password"], "support": {"source": "https://github.com/symfony/password-hasher/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-04T08:22:58+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "reference": "b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "3833d7255cc303546435cb650316bff708a1c75c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/3833d7255cc303546435cb650316bff708a1c75c", "reference": "3833d7255cc303546435cb650316bff708a1c75c", "shasum": ""}, "require": {"php": ">=7.2"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "reference": "21533be36c24be3f4b1669c4725c7d1d2bab4ae2", "shasum": ""}, "require": {"php": ">=7.2"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-09T11:45:10+00:00"}, {"name": "symfony/process", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "reference": "40c295f2deb408d5e9d2d32b8ba1dd61e36f05af", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-17T09:11:12+00:00"}, {"name": "symfony/property-access", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "518d15c8cca726ebe665dcd7154074584cf862e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/518d15c8cca726ebe665dcd7154074584cf862e8", "reference": "518d15c8cca726ebe665dcd7154074584cf862e8", "shasum": ""}, "require": {"php": ">=8.2", "symfony/property-info": "^6.4|^7.0"}, "require-dev": {"symfony/cache": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T04:04:43+00:00"}, {"name": "symfony/property-info", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "90586acbf2a6dd13bee4f09f09111c8bd4773970"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/90586acbf2a6dd13bee4f09f09111c8bd4773970", "reference": "90586acbf2a6dd13bee4f09f09111c8bd4773970", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0", "symfony/type-info": "~7.2.8|^7.3.1"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/cache": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/routing", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "8e213820c5fea844ecea29203d2a308019007c15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/8e213820c5fea844ecea29203d2a308019007c15", "reference": "8e213820c5fea844ecea29203d2a308019007c15", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-24T20:43:28+00:00"}, {"name": "symfony/runtime", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/runtime.git", "reference": "9516056d432f8acdac9458eb41b80097da7a05c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/runtime/zipball/9516056d432f8acdac9458eb41b80097da7a05c9", "reference": "9516056d432f8acdac9458eb41b80097da7a05c9", "shasum": ""}, "require": {"composer-plugin-api": "^1.0|^2.0", "php": ">=8.2"}, "conflict": {"symfony/dotenv": "<6.4"}, "require-dev": {"composer/composer": "^2.6", "symfony/console": "^6.4|^7.0", "symfony/dotenv": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "composer-plugin", "extra": {"class": "Symfony\\Component\\Runtime\\Internal\\ComposerPlugin"}, "autoload": {"psr-4": {"Symfony\\Component\\Runtime\\": "", "Symfony\\Runtime\\Symfony\\Component\\": "Internal/"}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Enables decoupling PHP applications from global state", "homepage": "https://symfony.com", "keywords": ["runtime"], "support": {"source": "https://github.com/symfony/runtime/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-13T07:48:40+00:00"}, {"name": "symfony/security-bundle", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/security-bundle.git", "reference": "428a281fd66c8358adc2259c8578e6d81fbb7079"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-bundle/zipball/428a281fd66c8358adc2259c8578e6d81fbb7079", "reference": "428a281fd66c8358adc2259c8578e6d81fbb7079", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "ext-xml": "*", "php": ">=8.2", "symfony/clock": "^6.4|^7.0", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4.11|^7.1.4", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/password-hasher": "^6.4|^7.0", "symfony/security-core": "^7.3", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^7.3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/browser-kit": "<6.4", "symfony/console": "<6.4", "symfony/framework-bundle": "<6.4", "symfony/http-client": "<6.4", "symfony/ldap": "<6.4", "symfony/serializer": "<6.4", "symfony/twig-bundle": "<6.4", "symfony/validator": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/dom-crawler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/twig": "^3.12", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\SecurityBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of the Security component into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-bundle/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T04:04:43+00:00"}, {"name": "symfony/security-core", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/security-core.git", "reference": "fafab1003a31e51506e1a0a83e81c072211d81ba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-core/zipball/fafab1003a31e51506e1a0a83e81c072211d81ba", "reference": "fafab1003a31e51506e1a0a83e81c072211d81ba", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/event-dispatcher-contracts": "^2.5|^3", "symfony/password-hasher": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-foundation": "<6.4", "symfony/ldap": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/validator": "<6.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "psr/container": "^1.1|^2.0", "psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/ldap": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Core\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - Core Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-core/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-23T07:28:50+00:00"}, {"name": "symfony/security-csrf", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/security-csrf.git", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-csrf/zipball/2b4b0c46c901729e4e90719eacd980381f53e0a3", "reference": "2b4b0c46c901729e4e90719eacd980381f53e0a3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/security-core": "^6.4|^7.0"}, "conflict": {"symfony/http-foundation": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Csrf\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - CSRF Library", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-csrf/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T18:42:10+00:00"}, {"name": "symfony/security-http", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/security-http.git", "reference": "b7182ed0fd2359297f78ff6d407265168255ea84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/security-http/zipball/b7182ed0fd2359297f78ff6d407265168255ea84", "reference": "b7182ed0fd2359297f78ff6d407265168255ea84", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/polyfill-mbstring": "~1.0", "symfony/property-access": "^6.4|^7.0", "symfony/security-core": "^7.3", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/clock": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/http-client-contracts": "<3.0", "symfony/security-bundle": "<6.4", "symfony/security-csrf": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/cache": "^6.4|^7.0", "symfony/clock": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-client-contracts": "^3.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "web-token/jwt-library": "^3.3.2|^4.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Security\\Http\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Security Component - HTTP Integration", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/security-http/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T04:04:43+00:00"}, {"name": "symfony/serializer", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/serializer.git", "reference": "feaf837cedbbc8287986602223175d3fd639922d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/serializer/zipball/feaf837cedbbc8287986602223175d3fd639922d", "reference": "feaf837cedbbc8287986602223175d3fd639922d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<6.4", "symfony/property-access": "<6.4", "symfony/property-info": "<6.4", "symfony/uid": "<6.4", "symfony/validator": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^3.2|^4.0|^5.0", "phpstan/phpdoc-parser": "^1.0|^2.0", "seld/jsonlint": "^1.10", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^7.2", "symfony/error-handler": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3", "symfony/type-info": "^7.1", "symfony/uid": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0", "symfony/var-exporter": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Serializer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Handles serializing and deserializing data structures, including object graphs, into array structures or other formats like XML and JSON.", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/serializer/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/service-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "reference": "f021b05a130d35510bd6b25fe9053c2a8a15d5d4", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-25T09:37:31+00:00"}, {"name": "symfony/string", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f3570b8c61ca887a9e2938e85cb6458515d2b125", "reference": "f3570b8c61ca887a9e2938e85cb6458515d2b125", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-20T20:19:01+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "reference": "df210c7a2573f1913b2d17cc95f90f53a73d8f7d", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/contracts", "name": "symfony/contracts"}, "branch-alias": {"dev-main": "3.6-dev"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.6.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-27T08:32:26+00:00"}, {"name": "symfony/twig-bridge", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/twig-bridge.git", "reference": "082eb15d8a4f9afee0acc4709fbe3aaf26d48891"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bridge/zipball/082eb15d8a4f9afee0acc4709fbe3aaf26d48891", "reference": "082eb15d8a4f9afee0acc4709fbe3aaf26d48891", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/translation-contracts": "^2.5|^3", "twig/twig": "^3.21"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/console": "<6.4", "symfony/form": "<6.4", "symfony/http-foundation": "<6.4", "symfony/http-kernel": "<6.4", "symfony/mime": "<6.4", "symfony/serializer": "<6.4", "symfony/translation": "<6.4", "symfony/workflow": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/asset": "^6.4|^7.0", "symfony/asset-mapper": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/emoji": "^7.1", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4.20|^7.2.5", "symfony/html-sanitizer": "^6.4|^7.0", "symfony/http-foundation": "^7.3", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/polyfill-intl-icu": "~1.0", "symfony/property-info": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/security-acl": "^2.8|^3.0", "symfony/security-core": "^6.4|^7.0", "symfony/security-csrf": "^6.4|^7.0", "symfony/security-http": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/workflow": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0", "twig/cssinliner-extra": "^3", "twig/inky-extra": "^3", "twig/markdown-extra": "^3"}, "type": "symfony-bridge", "autoload": {"psr-4": {"Symfony\\Bridge\\Twig\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides integration for Twig with various Symfony components", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bridge/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-19T13:28:56+00:00"}, {"name": "symfony/twig-bundle", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/twig-bundle.git", "reference": "bc23c11d9716fc2261ee26a32e654b0e8b1b1896"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/twig-bundle/zipball/bc23c11d9716fc2261ee26a32e654b0e8b1b1896", "reference": "bc23c11d9716fc2261ee26a32e654b0e8b1b1896", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^7.3", "symfony/dependency-injection": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/twig-bridge": "^7.3", "twig/twig": "^3.12"}, "conflict": {"symfony/framework-bundle": "<6.4", "symfony/translation": "<6.4"}, "require-dev": {"symfony/asset": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/form": "^6.4|^7.0", "symfony/framework-bundle": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/translation": "^6.4|^7.0", "symfony/web-link": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\TwigBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a tight integration of Twig into the Symfony full-stack framework", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/twig-bundle/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-24T04:04:43+00:00"}, {"name": "symfony/type-info", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "5fa6e25e4195e73ce9e457b521ac5e61ec271150"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/5fa6e25e4195e73ce9e457b521ac5e61ec271150", "reference": "5fa6e25e4195e73ce9e457b521ac5e61ec271150", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"phpstan/phpdoc-parser": "<1.30"}, "require-dev": {"phpstan/phpdoc-parser": "^1.30|^2.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/uid", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "a69f69f3159b852651a6bf45a9fdd149520525bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/a69f69f3159b852651a6bf45a9fdd149520525bb", "reference": "a69f69f3159b852651a6bf45a9fdd149520525bb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/validator", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "e2f2497c869fc57446f735fbf00cff4de32ae8c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/e2f2497c869fc57446f735fbf00cff4de32ae8c3", "reference": "e2f2497c869fc57446f735fbf00cff4de32ae8c3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php83": "^1.27", "symfony/translation-contracts": "^2.5|^3"}, "conflict": {"doctrine/lexer": "<1.1", "symfony/dependency-injection": "<6.4", "symfony/doctrine-bridge": "<7.0", "symfony/expression-language": "<6.4", "symfony/http-kernel": "<6.4", "symfony/intl": "<6.4", "symfony/property-info": "<6.4", "symfony/translation": "<6.4.3|>=7.0,<7.0.3", "symfony/yaml": "<6.4"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3|^4", "symfony/cache": "^6.4|^7.0", "symfony/config": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/string": "^6.4|^7.0", "symfony/translation": "^6.4.3|^7.0.3", "symfony/type-info": "^7.1", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/", "/Resources/bin/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-26T13:22:23+00:00"}, {"name": "symfony/var-dumper", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "6e209fbe5f5a7b6043baba46fe5735a4b85d0d42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/6e209fbe5f5a7b6043baba46fe5735a4b85d0d42", "reference": "6e209fbe5f5a7b6043baba46fe5735a4b85d0d42", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/uid": "^6.4|^7.0", "twig/twig": "^3.12"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-27T19:55:54+00:00"}, {"name": "symfony/var-exporter", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/c9a1168891b5aaadfd6332ef44393330b3498c4c", "reference": "c9a1168891b5aaadfd6332ef44393330b3498c4c", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-05-15T09:04:05+00:00"}, {"name": "symfony/yaml", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/0c3555045a46ab3cd4cc5a69d161225195230edb", "reference": "0c3555045a46ab3cd4cc5a69d161225195230edb", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-03T06:57:57+00:00"}, {"name": "twig/twig", "version": "v3.21.1", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/285123877d4dd97dd7c11842ac5fb7e86e60d81d", "reference": "285123877d4dd97dd7c11842ac5fb7e86e60d81d", "shasum": ""}, "require": {"php": ">=8.1.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"phpstan/phpstan": "^2.0", "psr/container": "^1.0|^2.0", "symfony/phpunit-bridge": "^5.4.9|^6.4|^7.0"}, "type": "library", "autoload": {"files": ["src/Resources/core.php", "src/Resources/debug.php", "src/Resources/escaper.php", "src/Resources/string_loader.php"], "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v3.21.1"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "time": "2025-05-03T07:21:55+00:00"}, {"name": "vuryss/dokky", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/vuryss/dokky.git", "reference": "5bb63f2e86966a51062259f3a6e0a07e8147fe55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vuryss/dokky/zipball/5bb63f2e86966a51062259f3a6e0a07e8147fe55", "reference": "5bb63f2e86966a51062259f3a6e0a07e8147fe55", "shasum": ""}, "require": {"php": "^8.4", "phpdocumentor/reflection-docblock": "^5.6", "phpstan/phpdoc-parser": "^2.0", "symfony/property-info": "^7.2"}, "require-dev": {"captainhook/captainhook": "^5.25", "captainhook/hook-installer": "^1.0", "friendsofphp/php-cs-fixer": "^3.70", "justinrainbow/json-schema": "^5.3", "pestphp/pest": "^3.7", "phpstan/phpstan": "^2.1", "ramsey/conventional-commits": "1.5.1", "symfony/serializer": "^7.2", "symfony/validator": "^7.2", "symfony/var-dumper": "^7.2", "vuryss/serializer": "^1.9"}, "type": "library", "autoload": {"psr-4": {"Dokky\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "OpenAPI 3.1 documentation generator for PHP", "support": {"issues": "https://github.com/vuryss/dokky/issues", "source": "https://github.com/vuryss/dokky/tree/v1.6.0"}, "time": "2025-05-08T08:29:16+00:00"}, {"name": "vuryss/serializer", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/vuryss/serializer.git", "reference": "e5811dff57a69dc8f228c083cb11e803944cdccc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vuryss/serializer/zipball/e5811dff57a69dc8f228c083cb11e803944cdccc", "reference": "e5811dff57a69dc8f228c083cb11e803944cdccc", "shasum": ""}, "require": {"php": "^8.4", "phpdocumentor/reflection-docblock": "^5.6.1", "phpstan/phpdoc-parser": "^2.1", "psr/cache": "^3.0", "symfony/property-info": "^7.2.5"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.75", "mockery/mockery": "^1.6.12", "pestphp/pest": "^2.36.0", "pestphp/pest-plugin-faker": "^2.0", "phpstan/phpstan": "^2.1.11", "symfony/serializer": "^7.2.5"}, "type": "library", "extra": {"symfony": {"require": "^7.2", "allow-contrib": false}}, "autoload": {"psr-4": {"Vuryss\\Serializer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A fast, extensible PHP data structures serializer & deserializer", "support": {"issues": "https://github.com/vuryss/serializer/issues", "source": "https://github.com/vuryss/serializer/tree/v1.11.0"}, "time": "2025-04-01T07:33:27+00:00"}, {"name": "web-token/jwt-library", "version": "4.0.4", "source": {"type": "git", "url": "https://github.com/web-token/jwt-library.git", "reference": "650108fa2cdd6cbaaead0dc0ab5302e178b23b0a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/web-token/jwt-library/zipball/650108fa2cdd6cbaaead0dc0ab5302e178b23b0a", "reference": "650108fa2cdd6cbaaead0dc0ab5302e178b23b0a", "shasum": ""}, "require": {"brick/math": "^0.12 || ^0.13", "ext-json": "*", "php": ">=8.2", "psr/clock": "^1.0", "spomky-labs/pki-framework": "^1.2.1"}, "conflict": {"spomky-labs/jose": "*"}, "suggest": {"ext-bcmath": "GMP or BCMath is highly recommended to improve the library performance", "ext-gmp": "GMP or BCMath is highly recommended to improve the library performance", "ext-openssl": "For key management (creation, optimization, etc.) and some algorithms (AES, RSA, ECDSA, etc.)", "ext-sodium": "Sodium is required for OKP key creation, EdDSA signature algorithm and ECDH-ES key encryption with OKP keys", "paragonie/sodium_compat": "Sodium is required for OKP key creation, EdDSA signature algorithm and ECDH-ES key encryption with OKP keys", "spomky-labs/aes-key-wrap": "For all Key Wrapping algorithms (AxxxKW, AxxxGCMKW, PBES2-HSxxx+AyyyKW...)", "symfony/console": "Needed to use console commands", "symfony/http-client": "To enable JKU/X5U support."}, "type": "library", "autoload": {"psr-4": {"Jose\\Component\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Florent <PERSON>", "homepage": "https://github.com/Spomky"}, {"name": "All contributors", "homepage": "https://github.com/web-token/jwt-framework/contributors"}], "description": "JWT library", "homepage": "https://github.com/web-token", "keywords": ["JOSE", "JWE", "JWK", "JWKSet", "JWS", "<PERSON><PERSON>", "RFC7515", "RFC7516", "RFC7517", "RFC7518", "RFC7519", "RFC7520", "bundle", "jwa", "jwt", "symfony"], "support": {"issues": "https://github.com/web-token/jwt-library/issues", "source": "https://github.com/web-token/jwt-library/tree/4.0.4"}, "funding": [{"url": "https://github.com/Spomky", "type": "github"}, {"url": "https://www.patreon.com/Florent<PERSON>orselli", "type": "patreon"}], "time": "2025-03-12T11:25:35+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "behat/gherkin", "version": "v4.14.0", "source": {"type": "git", "url": "https://github.com/Behat/Gherkin.git", "reference": "34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Behat/Gherkin/zipball/34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4", "reference": "34c9b59c59355a7b4c53b9f041c8dbd1c8acc3b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.2", "php": "8.1.* || 8.2.* || 8.3.* || 8.4.*"}, "require-dev": {"cucumber/gherkin-monorepo": "dev-gherkin-v32.1.1", "friendsofphp/php-cs-fixer": "^3.65", "mikey179/vfsstream": "^1.6", "phpstan/extension-installer": "^1", "phpstan/phpstan": "^2", "phpstan/phpstan-phpunit": "^2", "phpunit/phpunit": "^10.5", "symfony/yaml": "^5.4 || ^6.4 || ^7.0"}, "suggest": {"symfony/yaml": "If you want to parse features, represented in YAML files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"Behat\\Gherkin\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://everzet.com"}], "description": "Gherkin DSL parser for PHP", "homepage": "https://behat.org/", "keywords": ["BDD", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>ber", "DSL", "g<PERSON>kin", "parser"], "support": {"issues": "https://github.com/Behat/Gher<PERSON>/issues", "source": "https://github.com/Behat/Gherkin/tree/v4.14.0"}, "time": "2025-05-23T15:06:40+00:00"}, {"name": "captainhook/captainhook", "version": "5.25.6", "source": {"type": "git", "url": "https://github.com/captainhook-git/captainhook.git", "reference": "8d84101821228609d48f0f2439ed29c9915002e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/captainhook-git/captainhook/zipball/8d84101821228609d48f0f2439ed29c9915002e9", "reference": "8d84101821228609d48f0f2439ed29c9915002e9", "shasum": ""}, "require": {"captainhook/secrets": "^0.9.4", "ext-json": "*", "ext-spl": "*", "ext-xml": "*", "php": ">=8.0", "sebastianfeldmann/camino": "^0.9.2", "sebastianfeldmann/cli": "^3.3", "sebastianfeldmann/git": "^3.14", "symfony/console": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/filesystem": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0", "symfony/process": "^2.7 || ^3.0 || ^4.0 || ^5.0 || ^6.0 || ^7.0"}, "replace": {"sebastianfeldmann/captainhook": "*"}, "require-dev": {"composer/composer": "~1 || ^2.0", "mikey179/vfsstream": "~1"}, "bin": ["bin/captainhook"], "type": "library", "extra": {"captainhook": {"config": "captainhook.json"}, "branch-alias": {"dev-main": "6.0.x-dev"}}, "autoload": {"psr-4": {"CaptainHook\\App\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP git hook manager", "homepage": "http://php.captainhook.info/", "keywords": ["commit-msg", "git", "hooks", "post-merge", "pre-commit", "pre-push", "prepare-commit-msg"], "support": {"issues": "https://github.com/captainhook-git/captainhook/issues", "source": "https://github.com/captainhook-git/captainhook/tree/5.25.6"}, "funding": [{"url": "https://github.com/sponsors/sebas<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2025-07-02T22:06:49+00:00"}, {"name": "captainhook/hook-installer", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/captainhook-git/hook-installer.git", "reference": "fb3c45f6204b08baba999f4ffc4ae707bf684e8b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/captainhook-git/hook-installer/zipball/fb3c45f6204b08baba999f4ffc4ae707bf684e8b", "reference": "fb3c45f6204b08baba999f4ffc4ae707bf684e8b", "shasum": ""}, "require": {"composer-plugin-api": "^1.1|^2.0", "php": ">=8.0"}, "require-dev": {"composer/composer": "*"}, "type": "composer-plugin", "extra": {"class": "CaptainHook\\HookInstaller\\ComposerPlugin"}, "autoload": {"psr-4": {"CaptainHook\\HookInstaller\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Composer Plugin that makes everyone activate the CaptainHook git hooks locally", "support": {"issues": "https://github.com/captainhook-git/hook-installer/issues", "source": "https://github.com/captainhook-git/hook-installer/tree/1.0.4"}, "time": "2025-04-08T07:12:26+00:00"}, {"name": "captainhook/secrets", "version": "0.9.7", "source": {"type": "git", "url": "https://github.com/captainhook-git/secrets.git", "reference": "d62c97f75f81ac98e22f1c282482bd35fa82f631"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/captainhook-git/secrets/zipball/d62c97f75f81ac98e22f1c282482bd35fa82f631", "reference": "d62c97f75f81ac98e22f1c282482bd35fa82f631", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.0"}, "type": "library", "autoload": {"psr-4": {"CaptainHook\\Secrets\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Utility classes to detect secrets", "keywords": ["commit-msg", "keys", "passwords", "post-merge", "prepare-commit-msg", "secrets", "tokens"], "support": {"issues": "https://github.com/captainhook-git/secrets/issues", "source": "https://github.com/captainhook-git/secrets/tree/0.9.7"}, "funding": [{"url": "https://github.com/sponsors/sebas<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2025-04-08T07:10:48+00:00"}, {"name": "clue/ndjson-react", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/clue/reactphp-ndjson.git", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/reactphp-ndjson/zipball/392dc165fce93b5bb5c637b67e59619223c931b0", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "support": {"issues": "https://github.com/clue/reactphp-ndjson/issues", "source": "https://github.com/clue/reactphp-ndjson/tree/v1.3.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-12-23T10:58:28+00:00"}, {"name": "codeception/codeception", "version": "5.3.2", "source": {"type": "git", "url": "https://github.com/Codeception/Codeception.git", "reference": "582112d7a603d575e41638df1e96900b10ae91b8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Codeception/zipball/582112d7a603d575e41638df1e96900b10ae91b8", "reference": "582112d7a603d575e41638df1e96900b10ae91b8", "shasum": ""}, "require": {"behat/gherkin": "^4.12", "codeception/lib-asserts": "^2.2", "codeception/stub": "^4.1", "ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.2", "phpunit/php-code-coverage": "^9.2 | ^10.0 | ^11.0 | ^12.0", "phpunit/php-text-template": "^2.0 | ^3.0 | ^4.0 | ^5.0", "phpunit/php-timer": "^5.0.3 | ^6.0 | ^7.0 | ^8.0", "phpunit/phpunit": "^9.5.20 | ^10.0 | ^11.0 | ^12.0", "psy/psysh": "^0.11.2 | ^0.12", "sebastian/comparator": "^4.0.5 | ^5.0 | ^6.0 | ^7.0", "sebastian/diff": "^4.0.3 | ^5.0 | ^6.0 | ^7.0", "symfony/console": ">=5.4.24 <8.0", "symfony/css-selector": ">=5.4.24 <8.0", "symfony/event-dispatcher": ">=5.4.24 <8.0", "symfony/finder": ">=5.4.24 <8.0", "symfony/var-dumper": ">=5.4.24 <8.0", "symfony/yaml": ">=5.4.24 <8.0"}, "conflict": {"codeception/lib-innerbrowser": "<3.1.3", "codeception/module-filesystem": "<3.0", "codeception/module-phpbrowser": "<2.5"}, "replace": {"codeception/phpunit-wrapper": "*"}, "require-dev": {"codeception/lib-innerbrowser": "*@dev", "codeception/lib-web": "*@dev", "codeception/module-asserts": "*@dev", "codeception/module-cli": "*@dev", "codeception/module-db": "*@dev", "codeception/module-filesystem": "*@dev", "codeception/module-phpbrowser": "*@dev", "codeception/module-webdriver": "*@dev", "codeception/util-universalframework": "*@dev", "doctrine/orm": "^3.3", "ext-simplexml": "*", "jetbrains/phpstorm-attributes": "^1.0", "laravel-zero/phar-updater": "^1.4", "php-webdriver/webdriver": "^1.15", "stecman/symfony-console-completion": "^0.14", "symfony/dotenv": ">=5.4.24 <8.0", "symfony/error-handler": ">=5.4.24 <8.0", "symfony/process": ">=5.4.24 <8.0", "vlucas/phpdotenv": "^5.1"}, "suggest": {"codeception/specify": "BDD-style code blocks", "codeception/verify": "BDD-style assertions", "ext-simplexml": "For loading params from XML files", "stecman/symfony-console-completion": "For BASH autocompletion", "symfony/dotenv": "For loading params from .env files", "symfony/phpunit-bridge": "For phpunit-bridge support", "vlucas/phpdotenv": "For loading params from .env files"}, "bin": ["codecept"], "type": "library", "extra": {"branch-alias": {"dev-main": "5.2.x-dev"}}, "autoload": {"files": ["functions.php"], "psr-4": {"Codeception\\": "src/Codeception", "Codeception\\Extension\\": "ext"}, "classmap": ["src/PHPUnit/TestCase.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codeception.com"}], "description": "BDD-style testing framework", "homepage": "https://codeception.com/", "keywords": ["BDD", "TDD", "acceptance testing", "functional testing", "unit testing"], "support": {"issues": "https://github.com/Codeception/Codeception/issues", "source": "https://github.com/Codeception/Codeception/tree/5.3.2"}, "funding": [{"url": "https://opencollective.com/codeception", "type": "open_collective"}], "time": "2025-05-26T07:47:39+00:00"}, {"name": "codeception/lib-asserts", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Codeception/lib-asserts.git", "reference": "06750a60af3ebc66faab4313981accec1be4eefc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-asserts/zipball/06750a60af3ebc66faab4313981accec1be4eefc", "reference": "06750a60af3ebc66faab4313981accec1be4eefc", "shasum": ""}, "require": {"codeception/phpunit-wrapper": "^7.7.1 | ^8.0.3 | ^9.0", "ext-dom": "*", "php": "^7.4 | ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Assertion methods used by Codeception core and Asserts module", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-asserts/issues", "source": "https://github.com/Codeception/lib-asserts/tree/2.2.0"}, "time": "2025-03-10T20:41:33+00:00"}, {"name": "codeception/lib-innerbrowser", "version": "4.0.6", "source": {"type": "git", "url": "https://github.com/Codeception/lib-innerbrowser.git", "reference": "74476dd019ec7900b26b7dca91a42fdcb04e549f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-innerbrowser/zipball/74476dd019ec7900b26b7dca91a42fdcb04e549f", "reference": "74476dd019ec7900b26b7dca91a42fdcb04e549f", "shasum": ""}, "require": {"codeception/codeception": "^5.0.8", "codeception/lib-web": "^1.0.1", "ext-dom": "*", "ext-json": "*", "ext-mbstring": "*", "php": "^8.1", "phpunit/phpunit": "^10.0 || ^11.0 || ^12.0", "symfony/browser-kit": "^4.4.24 || ^5.4 || ^6.0 || ^7.0", "symfony/dom-crawler": "^4.4.30 || ^5.4 || ^6.0 || ^7.0"}, "require-dev": {"codeception/util-universalframework": "^1.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://codegyre.com"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Parent library for all Codeception framework modules and PhpBrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-innerbrowser/issues", "source": "https://github.com/Codeception/lib-innerbrowser/tree/4.0.6"}, "time": "2025-02-14T07:02:48+00:00"}, {"name": "codeception/lib-web", "version": "1.0.7", "source": {"type": "git", "url": "https://github.com/Codeception/lib-web.git", "reference": "1444ccc9b1d6721f3ced8703c8f4a9041b80df93"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-web/zipball/1444ccc9b1d6721f3ced8703c8f4a9041b80df93", "reference": "1444ccc9b1d6721f3ced8703c8f4a9041b80df93", "shasum": ""}, "require": {"ext-mbstring": "*", "guzzlehttp/psr7": "^2.0", "php": "^8.1", "phpunit/phpunit": "^9.5 | ^10.0 | ^11.0 | ^12", "symfony/css-selector": ">=4.4.24 <8.0"}, "conflict": {"codeception/codeception": "<5.0.0-alpha3"}, "require-dev": {"php-webdriver/webdriver": "^1.12"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Library containing files used by module-webdriver and lib-innerbrowser or module-phpbrowser", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-web/issues", "source": "https://github.com/Codeception/lib-web/tree/1.0.7"}, "time": "2025-02-09T12:05:55+00:00"}, {"name": "codeception/lib-xml", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/Codeception/lib-xml.git", "reference": "ba49213e60807e3612513f404a5c93aec63f4c72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/lib-xml/zipball/ba49213e60807e3612513f404a5c93aec63f4c72", "reference": "ba49213e60807e3612513f404a5c93aec63f4c72", "shasum": ""}, "require": {"codeception/lib-web": "^1.0.6", "ext-dom": "*", "php": "^8.0", "symfony/css-selector": ">=4.4.24 <8.0"}, "conflict": {"codeception/codeception": "<5.0.0-alpha3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Files used by module-rest and module-soap", "homepage": "https://codeception.com/", "keywords": ["codeception"], "support": {"issues": "https://github.com/Codeception/lib-xml/issues", "source": "https://github.com/Codeception/lib-xml/tree/1.0.3"}, "time": "2024-02-06T21:00:41+00:00"}, {"name": "codeception/module-asserts", "version": "3.2.0", "source": {"type": "git", "url": "https://github.com/Codeception/module-asserts.git", "reference": "eb1f7c980423888f3def5116635754ae4a75bd47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-asserts/zipball/eb1f7c980423888f3def5116635754ae4a75bd47", "reference": "eb1f7c980423888f3def5116635754ae4a75bd47", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "codeception/lib-asserts": "^2.2", "php": "^8.2"}, "conflict": {"codeception/codeception": "<5.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "description": "Codeception module containing various assertions", "homepage": "https://codeception.com/", "keywords": ["assertions", "asserts", "codeception"], "support": {"issues": "https://github.com/Codeception/module-asserts/issues", "source": "https://github.com/Codeception/module-asserts/tree/3.2.0"}, "time": "2025-05-02T02:33:11+00:00"}, {"name": "codeception/module-phpbrowser", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-phpbrowser.git", "reference": "a972411f60cd00d00d5e5e3b35496ba4a23bcffc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-phpbrowser/zipball/a972411f60cd00d00d5e5e3b35496ba4a23bcffc", "reference": "a972411f60cd00d00d5e5e3b35496ba4a23bcffc", "shasum": ""}, "require": {"codeception/codeception": "*@dev", "codeception/lib-innerbrowser": "*@dev", "ext-json": "*", "guzzlehttp/guzzle": "^7.4", "php": "^8.0", "symfony/browser-kit": "^5.4 || ^6.0 || ^7.0"}, "conflict": {"codeception/codeception": "<5.0", "codeception/lib-innerbrowser": "<3.0"}, "require-dev": {"aws/aws-sdk-php": "^3.199", "codeception/module-rest": "^2.0 || *@dev", "ext-curl": "*"}, "suggest": {"codeception/phpbuiltinserver": "Start and stop PHP built-in web server for your tests"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "Codeception module for testing web application over HTTP", "homepage": "https://codeception.com/", "keywords": ["codeception", "functional-testing", "http"], "support": {"issues": "https://github.com/Codeception/module-phpbrowser/issues", "source": "https://github.com/Codeception/module-phpbrowser/tree/3.0.1"}, "time": "2023-12-08T19:41:28+00:00"}, {"name": "codeception/module-rest", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/Codeception/module-rest.git", "reference": "5db3a2e62ce6948d1822709c034a22fa1b1f2309"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/module-rest/zipball/5db3a2e62ce6948d1822709c034a22fa1b1f2309", "reference": "5db3a2e62ce6948d1822709c034a22fa1b1f2309", "shasum": ""}, "require": {"codeception/codeception": "^5.0.8", "codeception/lib-xml": "^1.0", "ext-dom": "*", "ext-json": "*", "justinrainbow/json-schema": "^5.2.9 || ^6", "php": "^8.1", "softcreatr/jsonpath": "^0.8 || ^0.9 || ^0.10"}, "require-dev": {"codeception/lib-innerbrowser": "^3.0 | ^4.0", "codeception/stub": "^4.0", "codeception/util-universalframework": "^1.0", "ext-libxml": "*", "ext-simplexml": "*"}, "suggest": {"aws/aws-sdk-php": "For using AWS Auth"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>"}], "description": "REST module for Codeception", "homepage": "https://codeception.com/", "keywords": ["codeception", "rest"], "support": {"issues": "https://github.com/Codeception/module-rest/issues", "source": "https://github.com/Codeception/module-rest/tree/3.4.1"}, "time": "2025-03-25T23:04:38+00:00"}, {"name": "codeception/stub", "version": "4.1.4", "source": {"type": "git", "url": "https://github.com/Codeception/Stub.git", "reference": "6ce453073a0c220b254dd7f4383645615e4071c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Codeception/Stub/zipball/6ce453073a0c220b254dd7f4383645615e4071c3", "reference": "6ce453073a0c220b254dd7f4383645615e4071c3", "shasum": ""}, "require": {"php": "^7.4 | ^8.0", "phpunit/phpunit": "^8.4 | ^9.0 | ^10.0 | ^11 | ^12"}, "conflict": {"codeception/codeception": "<5.0.6"}, "require-dev": {"consolidation/robo": "^3.0"}, "type": "library", "autoload": {"psr-4": {"Codeception\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Flexible Stub wrapper for PHPUnit's Mock Builder", "support": {"issues": "https://github.com/Codeception/Stub/issues", "source": "https://github.com/Codeception/Stub/tree/4.1.4"}, "time": "2025-02-14T06:56:33+00:00"}, {"name": "composer/ca-bundle", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "d665d22c417056996c59019579f1967dfe5c1e82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/d665d22c417056996c59019579f1967dfe5c1e82", "reference": "d665d22c417056996c59019579f1967dfe5c1e82", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.10", "phpunit/phpunit": "^8 || ^9", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/process": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-26T15:08:54+00:00"}, {"name": "composer/class-map-generator", "version": "1.6.1", "source": {"type": "git", "url": "https://github.com/composer/class-map-generator.git", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/class-map-generator/zipball/134b705ddb0025d397d8318a75825fe3c9d1da34", "reference": "134b705ddb0025d397d8318a75825fe3c9d1da34", "shasum": ""}, "require": {"composer/pcre": "^2.1 || ^3.1", "php": "^7.2 || ^8.0", "symfony/finder": "^4.4 || ^5.3 || ^6 || ^7"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-deprecation-rules": "^1 || ^2", "phpstan/phpstan-phpunit": "^1 || ^2", "phpstan/phpstan-strict-rules": "^1.1 || ^2", "phpunit/phpunit": "^8", "symfony/filesystem": "^5.4 || ^6"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\ClassMapGenerator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Utilities to scan PHP code and generate class maps.", "keywords": ["classmap"], "support": {"issues": "https://github.com/composer/class-map-generator/issues", "source": "https://github.com/composer/class-map-generator/tree/1.6.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-03-24T13:50:44+00:00"}, {"name": "composer/composer", "version": "2.8.10", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "53834f587d7ab2527eb237459d7b94d1fb9d4c5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/53834f587d7ab2527eb237459d7b94d1fb9d4c5a", "reference": "53834f587d7ab2527eb237459d7b94d1fb9d4c5a", "shasum": ""}, "require": {"composer/ca-bundle": "^1.5", "composer/class-map-generator": "^1.4.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^2.2 || ^3.2", "composer/semver": "^3.3", "composer/spdx-licenses": "^1.5.7", "composer/xdebug-handler": "^2.0.2 || ^3.0.3", "justinrainbow/json-schema": "^6.3.1", "php": "^7.2.5 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "react/promise": "^2.11 || ^3.2", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.2", "seld/signal-handler": "^2.0", "symfony/console": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/filesystem": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/finder": "^5.4.35 || ^6.3.12 || ^7.0.3", "symfony/polyfill-php73": "^1.24", "symfony/polyfill-php80": "^1.24", "symfony/polyfill-php81": "^1.24", "symfony/process": "^5.4.35 || ^6.3.12 || ^7.0.3"}, "require-dev": {"phpstan/phpstan": "^1.11.8", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpstan/phpstan-symfony": "^1.4.0", "symfony/phpunit-bridge": "^6.4.3 || ^7.0.1"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"phpstan": {"includes": ["phpstan/rules.neon"]}, "branch-alias": {"dev-main": "2.8-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "security": "https://github.com/composer/composer/security/policy", "source": "https://github.com/composer/composer/tree/2.8.10"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-07-10T17:08:33+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "3.3.2", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "reference": "b2bed4734f0cc156ee1fe9c0da2550420d99a21e", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "conflict": {"phpstan/phpstan": "<1.11.10"}, "require-dev": {"phpstan/phpstan": "^1.12 || ^2", "phpstan/phpstan-strict-rules": "^1 || ^2", "phpunit/phpunit": "^8 || ^9"}, "type": "library", "extra": {"phpstan": {"includes": ["extension.neon"]}, "branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.3.2"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-11-12T16:29:46+00:00"}, {"name": "composer/semver", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "reference": "4313d26ada5e0c4edfbd1dc481a92ff7bff91f12", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-09-19T14:15:21+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.9", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/edf364cefe8c43501e21e88110aac10b284c3c9f", "reference": "edf364cefe8c43501e21e88110aac10b284c3c9f", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "symfony/phpunit-bridge": "^3 || ^7"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.9"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2025-05-12T21:07:07+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "devizzent/cebe-php-openapi", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/DEVizzent/cebe-php-openapi.git", "reference": "af42b77f339b6b2920b65bae5df748e47391e11d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DEVizzent/cebe-php-openapi/zipball/af42b77f339b6b2920b65bae5df748e47391e11d", "reference": "af42b77f339b6b2920b65bae5df748e47391e11d", "shasum": ""}, "require": {"ext-json": "*", "justinrainbow/json-schema": "^5.2 || ^6.0", "php": ">=7.1.0", "symfony/yaml": "^3.4 || ^4 || ^5 || ^6 || ^7"}, "conflict": {"symfony/yaml": "3.4.0 - 3.4.4 || 4.0.0 - 4.4.17 || 5.0.0 - 5.1.9 || 5.2.0"}, "replace": {"cebe/php-openapi": "1.7.0"}, "require-dev": {"apis-guru/openapi-directory": "1.0.0", "cebe/indent": "*", "mermade/openapi3-examples": "1.0.0", "oai/openapi-specification-3.0": "3.0.3", "oai/openapi-specification-3.1": "3.1.0", "phpstan/phpstan": "^0.12.0", "phpunit/phpunit": "^6.5 || ^7.5 || ^8.5 || ^9.4 || ^11.4"}, "bin": ["bin/php-openapi"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}}, "autoload": {"psr-4": {"cebe\\openapi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://cebe.cc/", "role": "Creator"}, {"name": "Vicent <PERSON>", "email": "<EMAIL>"}], "description": "Read and write OpenAPI yaml/json files and make the content accessable in PHP objects.", "homepage": "https://github.com/DEVizzent/cebe-php-openapi#readme", "keywords": ["openapi"], "support": {"issues": "https://github.com/DEVizzent/cebe-php-openapi/issues", "source": "https://github.com/DEVizzent/cebe-php-openapi"}, "time": "2025-01-16T11:12:34+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "8520451a140d3f46ac33042715115e290cf5785f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/8520451a140d3f46ac33042715115e290cf5785f", "reference": "8520451a140d3f46ac33042715115e290cf5785f", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.2.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-08-06T10:04:20+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.84.0", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "38dad0767bf2a9b516b976852200ae722fe984ca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/PHP-CS-Fixer/zipball/38dad0767bf2a9b516b976852200ae722fe984ca", "reference": "38dad0767bf2a9b516b976852200ae722fe984ca", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.5", "ext-filter": "*", "ext-hash": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.2", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.6", "react/event-loop": "^1.0", "react/promise": "^2.11 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0.6 || ^5.1.1 || ^6.0.2 || ^7.0", "symfony/console": "^5.4.45 || ^6.4.13 || ^7.0", "symfony/event-dispatcher": "^5.4.45 || ^6.4.13 || ^7.0", "symfony/filesystem": "^5.4.45 || ^6.4.13 || ^7.0", "symfony/finder": "^5.4.45 || ^6.4.17 || ^7.0", "symfony/options-resolver": "^5.4.45 || ^6.4.16 || ^7.0", "symfony/polyfill-mbstring": "^1.32", "symfony/polyfill-php80": "^1.32", "symfony/polyfill-php81": "^1.32", "symfony/process": "^5.4.47 || ^6.4.20 || ^7.2", "symfony/stopwatch": "^5.4.45 || ^6.4.19 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3.1 || ^2.6", "infection/infection": "^0.29.14", "justinrainbow/json-schema": "^5.3 || ^6.4", "keradus/cli-executor": "^2.2", "mikey179/vfsstream": "^1.6.12", "php-coveralls/php-coveralls": "^2.8", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.6", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.6", "phpunit/phpunit": "^9.6.23 || ^10.5.47 || ^11.5.25", "symfony/polyfill-php84": "^1.32", "symfony/var-dumper": "^5.4.48 || ^6.4.23 || ^7.3.1", "symfony/yaml": "^5.4.45 || ^6.4.23 || ^7.3.1"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.84.0"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2025-07-15T18:21:57+00:00"}, {"name": "jawira/case-converter", "version": "v3.6.0", "source": {"type": "git", "url": "https://github.com/jawira/case-converter.git", "reference": "de9956122568743a83e0fc7e2eaa92c1b0de3f18"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jawira/case-converter/zipball/de9956122568743a83e0fc7e2eaa92c1b0de3f18", "reference": "de9956122568743a83e0fc7e2eaa92c1b0de3f18", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=7.4"}, "require-dev": {"behat/behat": "^3.0", "phpstan/phpstan": "^v2", "phpunit/phpunit": "^9.0"}, "suggest": {"pds/skeleton": "PHP Package Development Standards", "phing/phing": "PHP Build Tool"}, "type": "library", "autoload": {"psr-4": {"Jawira\\CaseConverter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Jawira Portugal", "email": "<EMAIL>"}], "description": "Convert strings between 13 naming conventions: Snake case, Camel case, Pascal case, Kebab case, Ada case, Train case, Cobol case, Macro case, Upper case, Lower case, Sentence case, Title case and Dot notation.", "homepage": "https://jawira.github.io/case-converter/", "keywords": ["Ada case", "Cobol case", "Macro case", "Train case", "camel case", "dot notation", "kebab case", "lower case", "pascal case", "sentence case", "snake case", "title case", "upper case"], "support": {"issues": "https://github.com/jawira/case-converter/issues", "source": "https://github.com/jawira/case-converter/tree/v3.6.0"}, "time": "2025-06-13T21:12:55+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "6.4.2", "source": {"type": "git", "url": "https://github.com/jsonrainbow/json-schema.git", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jsonrainbow/json-schema/zipball/ce1fd2d47799bb60668643bc6220f6278a4c1d02", "reference": "ce1fd2d47799bb60668643bc6220f6278a4c1d02", "shasum": ""}, "require": {"ext-json": "*", "marc-mabe/php-enum": "^4.0", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.3.0", "json-schema/json-schema-test-suite": "1.2.0", "marc-mabe/php-enum-phpstan": "^2.0", "phpspec/prophecy": "^1.19", "phpstan/phpstan": "^1.12", "phpunit/phpunit": "^8.5"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/jsonrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/jsonrainbow/json-schema/issues", "source": "https://github.com/jsonrainbow/json-schema/tree/6.4.2"}, "time": "2025-06-03T18:27:04+00:00"}, {"name": "league/openapi-psr7-validator", "version": "0.22", "source": {"type": "git", "url": "https://github.com/thephpleague/openapi-psr7-validator.git", "reference": "a665e220d0bba68411efc1899ed5022fd9b10113"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/openapi-psr7-validator/zipball/a665e220d0bba68411efc1899ed5022fd9b10113", "reference": "a665e220d0bba68411efc1899ed5022fd9b10113", "shasum": ""}, "require": {"devizzent/cebe-php-openapi": "^1.0", "ext-json": "*", "league/uri": "^6.3 || ^7.0", "php": ">=7.2", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-middleware": "^1.0", "respect/validation": "^1.1.3 || ^2.0", "riverline/multipart-parser": "^2.0.3", "symfony/polyfill-php80": "^1.27", "webmozart/assert": "^1.4"}, "require-dev": {"doctrine/coding-standard": "^8.0", "guzzlehttp/psr7": "^2.0", "hansott/psr7-cookies": "^3.0.2 || ^4.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-webmozart-assert": "^1", "phpunit/phpunit": "^7 || ^8 || ^9", "symfony/cache": "^5.1"}, "type": "library", "autoload": {"psr-4": {"League\\OpenAPIValidation\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Validate PSR-7 messages against OpenAPI (3.0.2) specifications expressed in YAML or JSON", "homepage": "https://github.com/thephpleague/openapi-psr7-validator", "keywords": ["http", "openapi", "psr7", "validation"], "support": {"issues": "https://github.com/thephpleague/openapi-psr7-validator/issues", "source": "https://github.com/thephpleague/openapi-psr7-validator/tree/0.22"}, "time": "2024-01-10T19:11:09+00:00"}, {"name": "league/uri", "version": "7.5.1", "source": {"type": "git", "url": "https://github.com/thephpleague/uri.git", "reference": "81fb5145d2644324614cc532b28efd0215bda430"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri/zipball/81fb5145d2644324614cc532b28efd0215bda430", "reference": "81fb5145d2644324614cc532b28efd0215bda430", "shasum": ""}, "require": {"league/uri-interfaces": "^7.5", "php": "^8.1"}, "conflict": {"league/uri-schemes": "^1.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-fileinfo": "to create Data URI from file contennts", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "jeremykendall/php-domain-parser": "to resolve Public Suffix and Top Level Domain", "league/uri-components": "Needed to easily manipulate URI objects components", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "URI manipulation library", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "middleware", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "uri-template", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri/tree/7.5.1"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:40:02+00:00"}, {"name": "league/uri-interfaces", "version": "7.5.0", "source": {"type": "git", "url": "https://github.com/thephpleague/uri-interfaces.git", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/uri-interfaces/zipball/08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "reference": "08cfc6c4f3d811584fb09c37e2849e6a7f9b0742", "shasum": ""}, "require": {"ext-filter": "*", "php": "^8.1", "psr/http-factory": "^1", "psr/http-message": "^1.1 || ^2.0"}, "suggest": {"ext-bcmath": "to improve IPV4 host parsing", "ext-gmp": "to improve IPV4 host parsing", "ext-intl": "to handle IDN host with the best performance", "php-64bit": "to improve IPV4 host parsing", "symfony/polyfill-intl-idn": "to handle IDN host via the Symfony polyfill if ext-intl is not present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"League\\Uri\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://nyamsprod.com"}], "description": "Common interfaces and classes for URI representation and interaction", "homepage": "https://uri.thephpleague.com", "keywords": ["data-uri", "file-uri", "ftp", "hostname", "http", "https", "parse_str", "parse_url", "psr-7", "query-string", "querystring", "rfc3986", "rfc3987", "rfc6570", "uri", "url", "ws"], "support": {"docs": "https://uri.thephpleague.com", "forum": "https://thephpleague.slack.com", "issues": "https://github.com/thephpleague/uri-src/issues", "source": "https://github.com/thephpleague/uri-interfaces/tree/7.5.0"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2024-12-08T08:18:47+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.7.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/7159809e5cfa041dca28e61f7f7ae58063aae8ed", "reference": "7159809e5cfa041dca28e61f7f7ae58063aae8ed", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0 | ^5.26.1"}, "type": "library", "extra": {"branch-alias": {"dev-3.x": "3.2-dev", "dev-master": "4.7-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}, "classmap": ["stubs/Stringable.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.7.1"}, "time": "2024-11-28T04:54:44+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "myclabs/deep-copy", "version": "1.13.3", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/faed855a7b5f4d4637717c2b3863e277116beb36", "reference": "faed855a7b5f4d4637717c2b3863e277116beb36", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.13.3"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2025-07-05T12:25:42+00:00"}, {"name": "nikic/php-parser", "version": "v5.5.0", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/ae59794362fe85e051a58ad36b289443f57be7a9", "reference": "ae59794362fe85e051a58ad36b289443f57be7a9", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "ext-tokenizer": "*", "php": ">=7.4"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v5.5.0"}, "time": "2025-05-31T08:24:38+00:00"}, {"name": "opis/json-schema", "version": "2.4.1", "source": {"type": "git", "url": "https://github.com/opis/json-schema.git", "reference": "712827751c62b465daae6e725bf0cf5ffbf965e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/json-schema/zipball/712827751c62b465daae6e725bf0cf5ffbf965e1", "reference": "712827751c62b465daae6e725bf0cf5ffbf965e1", "shasum": ""}, "require": {"ext-json": "*", "opis/string": "^2.0", "opis/uri": "^1.0", "php": "^7.4 || ^8.0"}, "require-dev": {"ext-bcmath": "*", "ext-intl": "*", "phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Opis\\JsonSchema\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Sorin Sarca", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "<PERSON><PERSON> Schema Validator for PHP", "homepage": "https://opis.io/json-schema", "keywords": ["json", "json-schema", "schema", "validation", "validator"], "support": {"issues": "https://github.com/opis/json-schema/issues", "source": "https://github.com/opis/json-schema/tree/2.4.1"}, "time": "2024-12-30T20:20:21+00:00"}, {"name": "opis/string", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/opis/string.git", "reference": "ba0b9607b9809462b0e28a11e4881a8d77431feb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/string/zipball/ba0b9607b9809462b0e28a11e4881a8d77431feb", "reference": "ba0b9607b9809462b0e28a11e4881a8d77431feb", "shasum": ""}, "require": {"ext-iconv": "*", "ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Opis\\String\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "Multibyte strings as objects", "homepage": "https://opis.io/string", "keywords": ["multi-byte", "opis", "string", "string manipulation", "utf-8"], "support": {"issues": "https://github.com/opis/string/issues", "source": "https://github.com/opis/string/tree/2.0.2"}, "time": "2024-12-30T21:43:22+00:00"}, {"name": "opis/uri", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/opis/uri.git", "reference": "0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/uri/zipball/0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a", "reference": "0f3ca49ab1a5e4a6681c286e0b2cc081b93a7d5a", "shasum": ""}, "require": {"opis/string": "^2.0", "php": "^7.4 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Opis\\Uri\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "Build, parse and validate URIs and URI-templates", "homepage": "https://opis.io", "keywords": ["URI Template", "parse url", "punycode", "uri", "uri components", "url", "validate uri"], "support": {"issues": "https://github.com/opis/uri/issues", "source": "https://github.com/opis/uri/tree/1.1.0"}, "time": "2021-05-22T15:57:08+00:00"}, {"name": "phar-io/manifest", "version": "2.0.4", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "54750ef60c58e43759730615a392c31c80e23176"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/54750ef60c58e43759730615a392c31c80e23176", "reference": "54750ef60c58e43759730615a392c31c80e23176", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-phar": "*", "ext-xmlwriter": "*", "phar-io/version": "^3.0.1", "php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/2.0.4"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:33:53+00:00"}, {"name": "phar-io/version", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "reference": "4f7fd7836c6f332bb2933569e566a0d6c4cbed74", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/3.2.1"}, "time": "2022-02-21T01:04:05+00:00"}, {"name": "phpstan/extension-installer", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/phpstan/extension-installer.git", "reference": "85e90b3942d06b2326fba0403ec24fe912372936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/extension-installer/zipball/85e90b3942d06b2326fba0403ec24fe912372936", "reference": "85e90b3942d06b2326fba0403ec24fe912372936", "shasum": ""}, "require": {"composer-plugin-api": "^2.0", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.9.0 || ^2.0"}, "require-dev": {"composer/composer": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.2.0", "phpstan/phpstan-strict-rules": "^0.11 || ^0.12 || ^1.0"}, "type": "composer-plugin", "extra": {"class": "PHPStan\\ExtensionInstaller\\Plugin"}, "autoload": {"psr-4": {"PHPStan\\ExtensionInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Composer plugin for automatic installation of PHPStan extensions", "keywords": ["dev", "static analysis"], "support": {"issues": "https://github.com/phpstan/extension-installer/issues", "source": "https://github.com/phpstan/extension-installer/tree/1.4.3"}, "time": "2024-09-04T20:21:43+00:00"}, {"name": "phpstan/phpstan", "version": "2.1.19", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "473a8c30e450d87099f76313edcbb90852f9afdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/473a8c30e450d87099f76313edcbb90852f9afdf", "reference": "473a8c30e450d87099f76313edcbb90852f9afdf", "shasum": ""}, "require": {"php": "^7.4|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}], "time": "2025-07-21T19:58:24+00:00"}, {"name": "phpstan/phpstan-symfony", "version": "2.0.7", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-symfony.git", "reference": "392f7ab8f52a0a776977be4e62535358c28e1b15"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-symfony/zipball/392f7ab8f52a0a776977be4e62535358c28e1b15", "reference": "392f7ab8f52a0a776977be4e62535358c28e1b15", "shasum": ""}, "require": {"ext-simplexml": "*", "php": "^7.4 || ^8.0", "phpstan/phpstan": "^2.1.13"}, "conflict": {"symfony/framework-bundle": "<3.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "psr/container": "1.1.2", "symfony/config": "^5.4 || ^6.1", "symfony/console": "^5.4 || ^6.1", "symfony/dependency-injection": "^5.4 || ^6.1", "symfony/form": "^5.4 || ^6.1", "symfony/framework-bundle": "^5.4 || ^6.1", "symfony/http-foundation": "^5.4 || ^6.1", "symfony/messenger": "^5.4", "symfony/polyfill-php80": "^1.24", "symfony/serializer": "^5.4", "symfony/service-contracts": "^2.2.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://lookyman.net"}], "description": "Symfony Framework extensions and rules for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-symfony/issues", "source": "https://github.com/phpstan/phpstan-symfony/tree/2.0.7"}, "time": "2025-07-22T09:40:57+00:00"}, {"name": "phpunit/php-code-coverage", "version": "12.3.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "ddec29dfc128eba9c204389960f2063f3b7fa170"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/ddec29dfc128eba9c204389960f2063f3b7fa170", "reference": "ddec29dfc128eba9c204389960f2063f3b7fa170", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "ext-xmlwriter": "*", "nikic/php-parser": "^5.4.0", "php": ">=8.3", "phpunit/php-file-iterator": "^6.0", "phpunit/php-text-template": "^5.0", "sebastian/complexity": "^5.0", "sebastian/environment": "^8.0", "sebastian/lines-of-code": "^4.0", "sebastian/version": "^6.0", "theseer/tokenizer": "^1.2.3"}, "require-dev": {"phpunit/phpunit": "^12.1"}, "suggest": {"ext-pcov": "PHP extension that provides line coverage", "ext-xdebug": "PHP extension that provides line coverage as well as branch and path coverage"}, "type": "library", "extra": {"branch-alias": {"dev-main": "12.3.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/12.3.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/php-code-coverage", "type": "tidelift"}], "time": "2025-06-18T08:58:13+00:00"}, {"name": "phpunit/php-file-iterator", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "961bc913d42fe24a257bfff826a5068079ac7782"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/961bc913d42fe24a257bfff826a5068079ac7782", "reference": "961bc913d42fe24a257bfff826a5068079ac7782", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:37+00:00"}, {"name": "phpunit/php-invoker", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-invoker.git", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-invoker/zipball/12b54e689b07a25a9b41e57736dfab6ec9ae5406", "reference": "12b54e689b07a25a9b41e57736dfab6ec9ae5406", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^12.0"}, "suggest": {"ext-pcntl": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Invoke callables with a timeout", "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "keywords": ["process"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-invoker/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:58+00:00"}, {"name": "phpunit/php-text-template", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/e1367a453f0eda562eedb4f659e13aa900d66c53", "reference": "e1367a453f0eda562eedb4f659e13aa900d66c53", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:59:16+00:00"}, {"name": "phpunit/php-timer", "version": "8.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc", "reference": "f258ce36aa457f3aa3339f9ed4c81fc66dc8c2cc", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-timer/tree/8.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:59:38+00:00"}, {"name": "phpunit/phpunit", "version": "12.2.7", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "8b1348b254e5959acaf1539c6bd790515fb49414"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/8b1348b254e5959acaf1539c6bd790515fb49414", "reference": "8b1348b254e5959acaf1539c6bd790515fb49414", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "ext-xmlwriter": "*", "myclabs/deep-copy": "^1.13.3", "phar-io/manifest": "^2.0.4", "phar-io/version": "^3.2.1", "php": ">=8.3", "phpunit/php-code-coverage": "^12.3.1", "phpunit/php-file-iterator": "^6.0.0", "phpunit/php-invoker": "^6.0.0", "phpunit/php-text-template": "^5.0.0", "phpunit/php-timer": "^8.0.0", "sebastian/cli-parser": "^4.0.0", "sebastian/comparator": "^7.1.0", "sebastian/diff": "^7.0.0", "sebastian/environment": "^8.0.2", "sebastian/exporter": "^7.0.0", "sebastian/global-state": "^8.0.0", "sebastian/object-enumerator": "^7.0.0", "sebastian/type": "^6.0.2", "sebastian/version": "^6.0.0", "staabm/side-effects-detector": "^1.0.5"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-main": "12.2-dev"}}, "autoload": {"files": ["src/Framework/Assert/Functions.php"], "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "security": "https://github.com/sebastian<PERSON>mann/phpunit/security/policy", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/12.2.7"}, "funding": [{"url": "https://phpunit.de/sponsors.html", "type": "custom"}, {"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/phpunit/phpunit", "type": "tidelift"}], "time": "2025-07-11T04:11:13+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psy/psysh", "version": "v0.12.9", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "1b801844becfe648985372cb4b12ad6840245ace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/1b801844becfe648985372cb4b12ad6840245ace", "reference": "1b801844becfe648985372cb4b12ad6840245ace", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "nikic/php-parser": "^5.0 || ^4.0", "php": "^8.0 || ^7.4", "symfony/console": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4", "symfony/var-dumper": "^7.0 || ^6.0 || ^5.0 || ^4.0 || ^3.4"}, "conflict": {"symfony/console": "4.4.37 || 5.3.14 || 5.3.15 || 5.4.3 || 5.4.4 || 6.0.3 || 6.0.4"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well."}, "bin": ["bin/psysh"], "type": "library", "extra": {"bamarni-bin": {"bin-links": false, "forward-command": false}, "branch-alias": {"dev-main": "0.12.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.12.9"}, "time": "2025-06-23T02:35:06+00:00"}, {"name": "ramsey/conventional-commits", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/ramsey/conventional-commits.git", "reference": "3eb46b9046d91f5b35462ff770a9d0326601783d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/conventional-commits/zipball/3eb46b9046d91f5b35462ff770a9d0326601783d", "reference": "3eb46b9046d91f5b35462ff770a9d0326601783d", "shasum": ""}, "require": {"composer-runtime-api": "^2.0", "composer/composer": "^2.4", "ext-json": "*", "jawira/case-converter": "^3.5", "opis/json-schema": "^2.3", "php": "^8.1", "symfony/console": "^6.0 || ^7.0", "symfony/filesystem": "^6.0 || ^7.0"}, "require-dev": {"captainhook/captainhook": "^5.15", "captainhook/plugin-composer": "^5.3", "ergebnis/composer-normalize": "^2.30", "hamcrest/hamcrest-php": "^2.0", "mockery/mockery": "^1.5", "php-parallel-lint/php-console-highlighter": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpstan/extension-installer": "^1.2", "phpstan/phpstan": "^1.10", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^10.1", "ramsey/coding-standard": "^2.3", "ramsey/composer-repl": "^1.4", "roave/security-advisories": "dev-latest", "sebastianfeldmann/cli": "^3.4", "sebastianfeldmann/git": "^3.8", "spatie/phpunit-snapshot-assertions": "^5.1", "symfony/process": "^6.0 || ^7.0"}, "suggest": {"captainhook/captainhook": "Manage your project's Git hooks with <PERSON><PERSON><PERSON>, and use ramsey/conventional-commits in your commit-msg and prepare-commit-msg hooks."}, "bin": ["bin/conventional-commits"], "type": "library", "extra": {"captainhook": {"force-install": true}, "branch-alias": {"dev-main": "1.x-dev"}, "ramsey/conventional-commits": {"configFile": "conventional-commits.json"}}, "autoload": {"psr-4": {"Ramsey\\CaptainHook\\": "src/<PERSON><PERSON><PERSON>/", "Ramsey\\ConventionalCommits\\": "src/ConventionalCommits/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}], "description": "A PHP library for creating and validating commit messages according to the Conventional Commits specification. Includes a CaptainHook action!", "keywords": ["HOOK", "captainhook", "commit", "commit-msg", "conventional", "conventional-commits", "git", "plugin"], "support": {"issues": "https://github.com/ramsey/conventional-commits/issues", "source": "https://github.com/ramsey/conventional-commits/tree/1.6.0"}, "funding": [{"url": "https://github.com/ramsey", "type": "github"}], "time": "2025-03-01T19:18:27+00:00"}, {"name": "react/cache", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/cache.git", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/cache/zipball/d47c472b64aa5608225f47965a484b75c7817d5b", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "support": {"issues": "https://github.com/reactphp/cache/issues", "source": "https://github.com/reactphp/cache/tree/v1.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2022-11-30T15:59:55+00:00"}, {"name": "react/child-process", "version": "v0.6.6", "source": {"type": "git", "url": "https://github.com/reactphp/child-process.git", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/child-process/zipball/1721e2b93d89b745664353b9cfc8f155ba8a6159", "reference": "1721e2b93d89b745664353b9cfc8f155ba8a6159", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/socket": "^1.16", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "type": "library", "autoload": {"psr-4": {"React\\ChildProcess\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "support": {"issues": "https://github.com/reactphp/child-process/issues", "source": "https://github.com/reactphp/child-process/tree/v0.6.6"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2025-01-01T16:37:48+00:00"}, {"name": "react/dns", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/reactphp/dns.git", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/dns/zipball/eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "support": {"issues": "https://github.com/reactphp/dns/issues", "source": "https://github.com/reactphp/dns/tree/v1.13.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-13T14:18:03+00:00"}, {"name": "react/event-loop", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/event-loop/zipball/bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.5.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-13T13:48:05+00:00"}, {"name": "react/promise", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-05-24T10:39:05+00:00"}, {"name": "react/socket", "version": "v1.16.0", "source": {"type": "git", "url": "https://github.com/reactphp/socket.git", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/socket/zipball/23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "reference": "23e4ff33ea3e160d2d1f59a0e6050e4b0fb0eac1", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.13", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.6 || ^1.2.1", "react/stream": "^1.4"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3.3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "support": {"issues": "https://github.com/reactphp/socket/issues", "source": "https://github.com/reactphp/socket/tree/v1.16.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-07-26T10:38:09+00:00"}, {"name": "react/stream", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/reactphp/stream.git", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/stream/zipball/1e5b0acb8fe55143b5b426817155190eb6f5b18d", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "support": {"issues": "https://github.com/reactphp/stream/issues", "source": "https://github.com/reactphp/stream/tree/v1.4.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-11T12:45:25+00:00"}, {"name": "respect/stringifier", "version": "0.2.0", "source": {"type": "git", "url": "https://github.com/Respect/Stringifier.git", "reference": "e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Respect/Stringifier/zipball/e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59", "reference": "e55af3c8aeaeaa2abb5fa47a58a8e9688cc23b59", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.8", "malukenho/docheader": "^0.1.7", "phpunit/phpunit": "^6.4"}, "type": "library", "autoload": {"files": ["src/stringify.php"], "psr-4": {"Respect\\Stringifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Respect/Stringifier Contributors", "homepage": "https://github.com/Respect/Stringifier/graphs/contributors"}], "description": "Converts any value to a string", "homepage": "http://respect.github.io/Stringifier/", "keywords": ["respect", "stringifier", "stringify"], "support": {"issues": "https://github.com/Respect/Stringifier/issues", "source": "https://github.com/Respect/Stringifier/tree/0.2.0"}, "time": "2017-12-29T19:39:25+00:00"}, {"name": "respect/validation", "version": "2.4.4", "source": {"type": "git", "url": "https://github.com/Respect/Validation.git", "reference": "f13f10f19978aea33af2a102a2f58f2db1e63619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Respect/Validation/zipball/f13f10f19978aea33af2a102a2f58f2db1e63619", "reference": "f13f10f19978aea33af2a102a2f58f2db1e63619", "shasum": ""}, "require": {"php": ">=8.1", "respect/stringifier": "^0.2.0", "symfony/polyfill-mbstring": "^1.2"}, "require-dev": {"egulias/email-validator": "^3.0", "giggsey/libphonenumber-for-php-lite": "^8.13 || ^9.0", "malukenho/docheader": "^1.0", "mikey179/vfsstream": "^1.6", "phpstan/phpstan": "^1.9", "phpstan/phpstan-deprecation-rules": "^1.1", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.6", "psr/http-message": "^1.0", "respect/coding-standard": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"egulias/email-validator": "Improves the Email rule if available", "ext-bcmath": "Arbitrary Precision Mathematics", "ext-fileinfo": "File Information", "ext-mbstring": "Multibyte String Functions", "giggsey/libphonenumber-for-php-lite": "Enables the phone rule if available"}, "type": "library", "autoload": {"psr-4": {"Respect\\Validation\\": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Respect/Validation Contributors", "homepage": "https://github.com/Respect/Validation/graphs/contributors"}], "description": "The most awesome validation engine ever created for PHP", "homepage": "http://respect.github.io/Validation/", "keywords": ["respect", "validation", "validator"], "support": {"issues": "https://github.com/Respect/Validation/issues", "source": "https://github.com/Respect/Validation/tree/2.4.4"}, "time": "2025-06-07T00:07:21+00:00"}, {"name": "riverline/multipart-parser", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Riverline/multipart-parser.git", "reference": "1410f23a8fd416a0cf5c8867ea9c95544016c831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Riverline/multipart-parser/zipball/1410f23a8fd416a0cf5c8867ea9c95544016c831", "reference": "1410f23a8fd416a0cf5c8867ea9c95544016c831", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=5.6.0"}, "require-dev": {"laminas/laminas-diactoros": "^1.8.7 || ^2.11.1", "phpunit/phpunit": "^5.7 || ^9.0", "psr/http-message": "^1.0", "symfony/psr-http-message-bridge": "^1.1 || ^2.0"}, "type": "library", "autoload": {"psr-4": {"Riverline\\MultiPartParser\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Riverline", "homepage": "http://www.riverline.fr"}], "description": "One class library to parse multipart content with encoding and charset support.", "keywords": ["http", "multipart", "parser"], "support": {"issues": "https://github.com/Riverline/multipart-parser/issues", "source": "https://github.com/Riverline/multipart-parser/tree/2.2.0"}, "time": "2025-04-29T08:38:14+00:00"}, {"name": "sebastian/cli-parser", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/cli-parser.git", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli-parser/zipball/6d584c727d9114bcdc14c86711cd1cad51778e7c", "reference": "6d584c727d9114bcdc14c86711cd1cad51778e7c", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for parsing CLI options", "homepage": "https://github.com/sebastian<PERSON>mann/cli-parser", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/cli-parser/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:53:50+00:00"}, {"name": "sebastian/comparator", "version": "7.1.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "03d905327dccc0851c9a08d6a979dfc683826b6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/03d905327dccc0851c9a08d6a979dfc683826b6f", "reference": "03d905327dccc0851c9a08d6a979dfc683826b6f", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "php": ">=8.3", "sebastian/diff": "^7.0", "sebastian/exporter": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.2"}, "suggest": {"ext-bcmath": "For comparing BcMath\\Number objects"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "security": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator/security/policy", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/7.1.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/comparator", "type": "tidelift"}], "time": "2025-06-17T07:41:58+00:00"}, {"name": "sebastian/complexity", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/complexity.git", "reference": "bad4316aba5303d0221f43f8cee37eb58d384bbb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/complexity/zipball/bad4316aba5303d0221f43f8cee37eb58d384bbb", "reference": "bad4316aba5303d0221f43f8cee37eb58d384bbb", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for calculating the complexity of PHP code units", "homepage": "https://github.com/sebastian<PERSON>mann/complexity", "support": {"issues": "https://github.com/sebastian<PERSON>mann/complexity/issues", "security": "https://github.com/sebastian<PERSON>mann/complexity/security/policy", "source": "https://github.com/sebastian<PERSON>mann/complexity/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:55:25+00:00"}, {"name": "sebastian/diff", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "7ab1ea946c012266ca32390913653d844ecd085f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/7ab1ea946c012266ca32390913653d844ecd085f", "reference": "7ab1ea946c012266ca32390913653d844ecd085f", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0", "symfony/process": "^7.2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:55:46+00:00"}, {"name": "sebastian/environment", "version": "8.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d364b9e5d0d3b18a2573351a1786fbf96b7e0792"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/environment/zipball/d364b9e5d0d3b18a2573351a1786fbf96b7e0792", "reference": "d364b9e5d0d3b18a2573351a1786fbf96b7e0792", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "https://github.com/sebastian<PERSON>mann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/environment/security/policy", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/8.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}, {"url": "https://liberapay.com/sebastian<PERSON>mann", "type": "liberapay"}, {"url": "https://thanks.dev/u/gh/sebas<PERSON><PERSON><PERSON>", "type": "thanks_dev"}, {"url": "https://tidelift.com/funding/github/packagist/sebastian/environment", "type": "tidelift"}], "time": "2025-05-21T15:05:44+00:00"}, {"name": "sebastian/exporter", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "76432aafc58d50691a00d86d0632f1217a47b688"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/76432aafc58d50691a00d86d0632f1217a47b688", "reference": "76432aafc58d50691a00d86d0632f1217a47b688", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.3", "sebastian/recursion-context": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "https://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "security": "https://github.com/sebastian<PERSON>mann/exporter/security/policy", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:56:42+00:00"}, {"name": "sebastian/global-state", "version": "8.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/global-state/zipball/570a2aeb26d40f057af686d63c4e99b075fb6cbc", "reference": "570a2aeb26d40f057af686d63c4e99b075fb6cbc", "shasum": ""}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"ext-dom": "*", "phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "8.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "https://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/global-state/security/policy", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/8.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:56:59+00:00"}, {"name": "sebastian/lines-of-code", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code.git", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/lines-of-code/zipball/97ffee3bcfb5805568d6af7f0f893678fc076d2f", "reference": "97ffee3bcfb5805568d6af7f0f893678fc076d2f", "shasum": ""}, "require": {"nikic/php-parser": "^5.0", "php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library for counting the lines of code in PHP source code", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/lines-of-code/tree/4.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:28+00:00"}, {"name": "sebastian/object-enumerator", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-enumerator/zipball/1effe8e9b8e068e9ae228e542d5d11b5d16db894", "reference": "1effe8e9b8e068e9ae228e542d5d11b5d16db894", "shasum": ""}, "require": {"php": ">=8.3", "sebastian/object-reflector": "^5.0", "sebastian/recursion-context": "^7.0"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:57:48+00:00"}, {"name": "sebastian/object-reflector", "version": "5.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "4bfa827c969c98be1e527abd576533293c634f6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/4bfa827c969c98be1e527abd576533293c634f6a", "reference": "4bfa827c969c98be1e527abd576533293c634f6a", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "5.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/5.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T04:58:17+00:00"}, {"name": "sebastian/recursion-context", "version": "7.0.0", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/c405ae3a63e01b32eb71577f8ec1604e39858a7c", "reference": "c405ae3a63e01b32eb71577f8ec1604e39858a7c", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/7.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:01+00:00"}, {"name": "sebastian/type", "version": "6.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/type.git", "reference": "1d7cd6e514384c36d7a390347f57c385d4be6069"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/type/zipball/1d7cd6e514384c36d7a390347f57c385d4be6069", "reference": "1d7cd6e514384c36d7a390347f57c385d4be6069", "shasum": ""}, "require": {"php": ">=8.3"}, "require-dev": {"phpunit/phpunit": "^12.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Collection of value objects that represent the types of the PHP type system", "homepage": "https://github.com/sebastian<PERSON>mann/type", "support": {"issues": "https://github.com/sebastian<PERSON>mann/type/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/type/security/policy", "source": "https://github.com/sebastian<PERSON>mann/type/tree/6.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-03-18T13:37:31+00:00"}, {"name": "sebastian/version", "version": "6.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/3e6ccf7657d4f0a59200564b08cead899313b53c", "reference": "3e6ccf7657d4f0a59200564b08cead899313b53c", "shasum": ""}, "require": {"php": ">=8.3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "security": "https://github.com/sebas<PERSON><PERSON>mann/version/security/policy", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/6.0.0"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2025-02-07T05:00:38+00:00"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/camino", "version": "0.9.5", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON><PERSON>/camino.git", "reference": "bf2e4c8b2a029e9eade43666132b61331e3e8184"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/camino/zipball/bf2e4c8b2a029e9eade43666132b61331e3e8184", "reference": "bf2e4c8b2a029e9eade43666132b61331e3e8184", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"SebastianFeldmann\\Camino\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Path management the OO way", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/camino", "keywords": ["file system", "path"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON><PERSON>/camino/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/camino/tree/0.9.5"}, "funding": [{"url": "https://github.com/sebas<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2022-01-03T13:15:10+00:00"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/cli", "version": "3.4.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON><PERSON>/cli.git", "reference": "6fa122afd528dae7d7ec988a604aa6c600f5d9b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/cli/zipball/6fa122afd528dae7d7ec988a604aa6c600f5d9b5", "reference": "6fa122afd528dae7d7ec988a604aa6c600f5d9b5", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"symfony/process": "^4.3 | ^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev"}}, "autoload": {"psr-4": {"SebastianFeldmann\\Cli\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP cli helper classes", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/cli", "keywords": ["cli"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON><PERSON>/cli/issues", "source": "https://github.com/sebastian<PERSON>mann/cli/tree/3.4.2"}, "funding": [{"url": "https://github.com/sebas<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2024-11-26T10:19:01+00:00"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>/git", "version": "3.14.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/git.git", "reference": "22584df8df01d95b0700000cfd855779fae7d8ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON><PERSON>/git/zipball/22584df8df01d95b0700000cfd855779fae7d8ea", "reference": "22584df8df01d95b0700000cfd855779fae7d8ea", "shasum": ""}, "require": {"ext-json": "*", "ext-libxml": "*", "ext-simplexml": "*", "php": ">=8.0", "sebastianfeldmann/cli": "^3.0"}, "require-dev": {"mikey179/vfsstream": "^1.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"SebastianFeldmann\\Git\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP git wrapper", "homepage": "https://github.com/sebastian<PERSON><PERSON>/git", "keywords": ["git"], "support": {"issues": "https://github.com/sebastian<PERSON><PERSON>/git/issues", "source": "https://github.com/sebastian<PERSON>mann/git/tree/3.14.3"}, "funding": [{"url": "https://github.com/sebas<PERSON><PERSON><PERSON>", "type": "github"}], "time": "2025-06-05T16:05:10+00:00"}, {"name": "seld/jsonlint", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/1748aaf847fc731cfad7725aec413ee46f0cc3a2", "reference": "1748aaf847fc731cfad7725aec413ee46f0cc3a2", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.11", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.11.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2024-07-11T14:55:45+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.1"}, "time": "2022-08-31T10:31:18+00:00"}, {"name": "seld/signal-handler", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/Seldaek/signal-handler.git", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/signal-handler/zipball/04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "reference": "04a6112e883ad76c0ada8e4a9f7520bbfdb6bb98", "shasum": ""}, "require": {"php": ">=7.2.0"}, "require-dev": {"phpstan/phpstan": "^1", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-phpunit": "^1", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "^7.5.20 || ^8.5.23", "psr/log": "^1 || ^2 || ^3"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Seld\\Signal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Simple unix signal handler that silently fails where signals are not supported for easy cross-platform development", "keywords": ["posix", "sigint", "signal", "sigterm", "unix"], "support": {"issues": "https://github.com/Seldaek/signal-handler/issues", "source": "https://github.com/Seldaek/signal-handler/tree/2.0.2"}, "time": "2023-09-03T09:24:00+00:00"}, {"name": "softcreatr/jsonpath", "version": "0.10.0", "source": {"type": "git", "url": "https://github.com/SoftCreatR/JSONPath.git", "reference": "74f0b330a98135160db947ba7bc65216b64a0c86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/SoftCreatR/JSONPath/zipball/74f0b330a98135160db947ba7bc65216b64a0c86", "reference": "74f0b330a98135160db947ba7bc65216b64a0c86", "shasum": ""}, "require": {"ext-json": "*", "php": "8.1 - 8.4"}, "replace": {"flow/jsonpath": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.58", "phpunit/phpunit": "10 - 12", "squizlabs/php_codesniffer": "^3.10"}, "type": "library", "autoload": {"psr-4": {"Flow\\JSONPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://prismaticbytes.com", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://1-2.dev", "role": "Developer"}], "description": "JSONPath implementation for parsing, searching and flattening arrays", "support": {"email": "<EMAIL>", "forum": "https://github.com/SoftCreatR/JSONPath/discussions", "issues": "https://github.com/SoftCreatR/JSONPath/issues", "source": "https://github.com/SoftCreatR/JSONPath"}, "funding": [{"url": "https://ecologi.com/softcreatr?r=61212ab3fc69b8eb8a2014f4", "type": "custom"}, {"url": "https://github.com/softcreatr", "type": "github"}], "time": "2025-03-22T00:28:17+00:00"}, {"name": "staabm/side-effects-detector", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/staabm/side-effects-detector.git", "reference": "d8334211a140ce329c13726d4a715adbddd0a163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/staabm/side-effects-detector/zipball/d8334211a140ce329c13726d4a715adbddd0a163", "reference": "d8334211a140ce329c13726d4a715adbddd0a163", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^1.12.6", "phpunit/phpunit": "^9.6.21", "symfony/var-dumper": "^5.4.43", "tomasvotruba/type-coverage": "1.0.0", "tomasvotruba/unused-public": "1.0.0"}, "type": "library", "autoload": {"classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A static analysis tool to detect side effects in PHP code", "keywords": ["static analysis"], "support": {"issues": "https://github.com/staabm/side-effects-detector/issues", "source": "https://github.com/staabm/side-effects-detector/tree/1.0.5"}, "funding": [{"url": "https://github.com/staabm", "type": "github"}], "time": "2024-10-20T05:08:20+00:00"}, {"name": "symfony/browser-kit", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/browser-kit.git", "reference": "5384291845e74fd7d54f3d925c4a86ce12336593"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/browser-kit/zipball/5384291845e74fd7d54f3d925c4a86ce12336593", "reference": "5384291845e74fd7d54f3d925c4a86ce12336593", "shasum": ""}, "require": {"php": ">=8.2", "symfony/dom-crawler": "^6.4|^7.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\BrowserKit\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Simulates the behavior of a web browser, allowing you to make requests, click on links and submit forms programmatically", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/browser-kit/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-03-05T10:15:41+00:00"}, {"name": "symfony/css-selector", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/601a5ce9aaad7bf10797e3663faefce9e26c24e2", "reference": "601a5ce9aaad7bf10797e3663faefce9e26c24e2", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-09-25T14:21:43+00:00"}, {"name": "symfony/dom-crawler", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "8b2ee2e06ab99fa5f067b6699296d4e35c156bb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/8b2ee2e06ab99fa5f067b6699296d4e35c156bb9", "reference": "8b2ee2e06ab99fa5f067b6699296d4e35c156bb9", "shasum": ""}, "require": {"masterminds/html5": "^2.6", "php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0"}, "require-dev": {"symfony/css-selector": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-15T10:07:06+00:00"}, {"name": "symfony/options-resolver", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/afb9a8038025e5dbc657378bfab9198d75f10fca", "reference": "afb9a8038025e5dbc657378bfab9198d75f10fca", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-04-04T13:12:05+00:00"}, {"name": "symfony/stopwatch", "version": "v7.3.0", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "reference": "5a49289e2b308214c8b9c2fda4ea454d8b8ad7cd", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-02-24T10:49:57+00:00"}, {"name": "symfony/web-profiler-bundle", "version": "v7.3.1", "source": {"type": "git", "url": "https://github.com/symfony/web-profiler-bundle.git", "reference": "47c994d8f08817122ffb48bf2ea4fb97b7e00d51"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-profiler-bundle/zipball/47c994d8f08817122ffb48bf2ea4fb97b7e00d51", "reference": "47c994d8f08817122ffb48bf2ea4fb97b7e00d51", "shasum": ""}, "require": {"composer-runtime-api": ">=2.1", "php": ">=8.2", "symfony/config": "^7.3", "symfony/deprecation-contracts": "^2.5|^3", "symfony/framework-bundle": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/twig-bundle": "^6.4|^7.0", "twig/twig": "^3.12"}, "conflict": {"symfony/form": "<6.4", "symfony/mailer": "<6.4", "symfony/messenger": "<6.4", "symfony/serializer": "<7.2", "symfony/workflow": "<7.3"}, "require-dev": {"symfony/browser-kit": "^6.4|^7.0", "symfony/console": "^6.4|^7.0", "symfony/css-selector": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0"}, "type": "symfony-bundle", "autoload": {"psr-4": {"Symfony\\Bundle\\WebProfilerBundle\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a development tool that gives detailed information about the execution of any request", "homepage": "https://symfony.com", "keywords": ["dev"], "support": {"source": "https://github.com/symfony/web-profiler-bundle/tree/v7.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-06-05T09:30:41+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "reference": "737eda637ed5e28c3413cb1ebe8bb52cbf1ca7a2", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/1.2.3"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2024-03-03T12:36:25+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": true, "prefer-lowest": false, "platform": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*"}, "platform-dev": {}, "plugin-api-version": "2.6.0"}