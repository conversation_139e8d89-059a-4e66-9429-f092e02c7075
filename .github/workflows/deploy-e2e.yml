name: E2E Deployment to k8s

on:
    workflow_dispatch:

env:
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}

concurrency:
    group: hermes-iam-deployment-e2e
    cancel-in-progress: false

jobs:
    resolve-tag:
        runs-on: prezero-github-runner
        outputs:
            tag: ${{ steps.tag.outputs.tag }}
        steps:
            -   name: Check if current github ref is a tag
                id: tag
                run: |
                    # Use a capture group () to extract the version number directly
                    if [[ "${{ github.ref }}" =~ ^refs/tags/v([0-9]+\.[0-9]+\.[0-9]+)$ ]]; then
                        # The first capture group is in BASH_REMATCH[1]
                        tag="${BASH_REMATCH[1]}"
                        echo "tag=$tag" >> "$GITHUB_OUTPUT"
                    else
                        echo "This action requires to be executed on a tag"
                        exit 1
                    fi

    deployment:
        runs-on: prezero-github-runner
        needs: [ resolve-tag ]
        environment: e2e
        steps:
            -   name: Get project files
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
                with:
                    repository: 'prezero/hermes-deployment'
                    ref: 'master'
                    token: ${{ secrets.PREZERO_GITHUB_TOKEN }}

            -   name: Update E2E hermes-iam tag
                run: echo ${{ needs.resolve-tag.outputs.tag }} > ./clusters/apps/hermes-iam/release/e2e.hermes-iam.txt

            -   name: Commit and push the updated tag
                uses: EndBug/add-and-commit@a94899bca583c204427a224a7af87c02f9b325d5 # v9
                with:
                    add: 'clusters/apps/hermes-iam/release/e2e.hermes-iam.txt'
                    message: 'Deploy E2E Hermes IAM: v${{ needs.resolve-tag.outputs.tag }}'
                    default_author: github_actions

            -   name: Argo CD Login
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: login argocd.e2e.hermes.runs.onstackit.cloud
                    options: --username admin --password ${{ secrets.ARGOCD_PASSWORD }}

            -   name: Argo CD Sync Hermes IAM
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app sync hermes-iam

            -   name: Set up Kubectl
                uses: azure/setup-kubectl@776406bce94f63e41d621b960d78ee25c8b76ede # v4
                with:
                    version: 'v1.32.5' # Specify the kubectl version that you want to use

            -   name: Configure Kubernetes Context
                uses: azure/k8s-set-context@212a19233d93f03eceaac31ae5a1d1acf650b6ef # v4
                with:
                    method: kubeconfig
                    kubeconfig: '${{ secrets.KUBECONFIG }}'

            -   name: Wait for hermes IAM deployment to be ready
                run: kubectl rollout status deployment -n hermes-iam --timeout=5m

    health-checks:
        runs-on: prezero-github-runner
        needs: [ deployment ]
        environment: e2e
        steps:
            -   name: Check the deployed service URL
                uses: jtalk/url-health-check-action@b716ccb6645355dd9fcce8002ce460e5474f7f00 # v4
                with:
                    url: 'https://iam.e2e.hermes.runs.onstackit.cloud/monitor/health/http_status_checks'
                    follow-redirect: false
                    max-attempts: 15 # Optional, defaults to 1
                    retry-delay: 5s # Optional, only applicable to max-attempts > 1
                    retry-all: true
