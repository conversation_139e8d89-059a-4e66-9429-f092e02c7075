name: "Pull Request checks"

on:
    workflow_dispatch:
    push:
        branches:
            - master
    pull_request:
        types:
            - opened
            - edited
            - reopened
            - synchronize

env:
    PREZERO_NPM_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'
    SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
    SNYK_ORG: ${{ vars.SNYK_ORG }}
    SNYK_ODJ_PRODUCT_ID: ${{ vars.SNYK_ODJ_PRODUCT_ID }}
    SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}

jobs:
    validate-pr-title:
        name: Validate PR title
        if: github.event_name == 'pull_request'
        runs-on: prezero-github-runner
        steps:
            - uses: amannn/action-semantic-pull-request@0723387faaf9b38adef4775cd42cfd5155ed6017 # v5
              env:
                  GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}

    check-actions:
        name: Validation github actions
        if: github.event_name == 'pull_request'
        runs-on: prezero-github-runner
        steps:
            -   name: Get project files
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
            -   name: Check workflow files
                uses: devops-actions/actionlint@c6744a34774e4e1c1df0ff66bdb07ec7ee480ca0 #v0.1.9

    code-analysis:
        runs-on: prezero-github-runner
        if: github.event_name == 'pull_request'
        container:
            image: ghcr.io/prezero/hermes-backend-images/base-php-dev:4.11.0@sha256:8d87dc8f39efd35da7ce176a531abe1f5816bc51990f08177ee986e7cdfb5674
            credentials:
                username: ${{ github.actor }}
                password: ${{ secrets.PREZERO_GITHUB_TOKEN }}
        env:
            APP_ENV: local
        steps:
            -   name: Get project files
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
            -   name: Composer check platform requirements
                run: composer check-platform-reqs
            -   name: Validate composer.json
                run: composer validate --strict
            -   name: Install dependencies
                run: composer install
            -   name: Check coding standards
                run: vendor/bin/php-cs-fixer fix --diff --dry-run --using-cache=no
                env:
                    PHP_CS_FIXER_IGNORE_ENV: 1
            -   name: Static code analysis & Architecture validation
                run: vendor/bin/phpstan analyse --memory-limit=1G -v
            -   name: Security check
                run: composer audit
