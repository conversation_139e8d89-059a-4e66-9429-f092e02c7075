name: DEV Deployment to k8s

on:
    push:
        branches:
            - master
    workflow_call:
    workflow_dispatch:

env:
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'

concurrency:
    group: hermes-iam-deployment-dev
    cancel-in-progress: false

jobs:
    check-github-action-syntax:
        runs-on: prezero-github-runner
        steps:
            -   name: Get project files
                uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4
            -   name: Check workflow files
                uses: devops-actions/actionlint@c6744a34774e4e1c1df0ff66bdb07ec7ee480ca0 #v0.1.9
    build-docker-image:
        name: Build Container Image
        uses: prezero/workflows/.github/workflows/docker-build.yaml@bb1bbb9c832d8ab928640d185f80e5f1799b053d # 1.31.1
        secrets: inherit
        with:
            ENV: ""
            DOCKERFILE: "docker/dev-k8s/Dockerfile"
            IMAGE: "ghcr.io/prezero/hermes-iam"
            TAG: "dev"
            TARGET_LAYER: ""
            RELEASE_CREATED: false
            BUILD_ARGS: ""
            # Re-enable when they release a new version
            enable_dockerfile_lint: "false"

    deployment:
        runs-on: prezero-github-runner
        needs: [ build-docker-image ]
        environment: dev

        steps:
            -   name: Argo CD Login
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: login argocd.dev.hermes.runs.onstackit.cloud
                    options: --username admin --password ${{ secrets.ARGOCD_PASSWORD }}

            -   name: Argo CD Sync Hermes IAM
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app sync hermes-iam
                    options: --prune

            -   name: Argo CD Sync External-Secrets
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app actions run hermes-iam
                    options: refresh --kind ExternalSecret --all

            -   name: Argo CD Restart All Deployments
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app actions run hermes-iam
                    options: restart --kind Deployment --all

            -   name: Argo CD wait for healthy Hermes IAM
                uses: clowdhaus/argo-cd-action@030da61d6d893fff21bb46a86a5ccd26a2dad8b2 # v3.0.0
                with:
                    command: app wait hermes-iam
                    options: --health -l 'argo-deploy=hermes-iam'

    health-checks:
        runs-on: prezero-github-runner
        needs: [ deployment ]
        environment: dev
        steps:
            -   name: Check the deployed service URL
                uses: jtalk/url-health-check-action@b716ccb6645355dd9fcce8002ce460e5474f7f00 # v4
                with:
                    url: 'https://iam.dev.hermes.runs.onstackit.cloud/monitor/health/http_status_checks'
                    follow-redirect: false
                    max-attempts: 15 # Optional, defaults to 1
                    retry-delay: 5s # Optional, only applicable to max-attempts > 1
                    retry-all: true

    test:
        runs-on: prezero-github-runner
        needs: [ health-checks ]
        environment: dev
        steps:
            -   name: Set up Kubectl
                uses: azure/setup-kubectl@776406bce94f63e41d621b960d78ee25c8b76ede # v4
                with:
                    version: 'v1.32.5' # Specify the kubectl version that you want to use

            -   name: Configure Kubernetes Context
                uses: azure/k8s-set-context@212a19233d93f03eceaac31ae5a1d1acf650b6ef # v4
                with:
                    method: kubeconfig
                    kubeconfig: '${{ secrets.KUBECONFIG }}'

            -   name: Execute Tests
                run: kubectl exec -n hermes-iam deployment/hermes-iam -- bash ./bin/api-tests
