name: Releases

on:
    push:
        branches:
            - master
    workflow_call:
    workflow_dispatch:

permissions:
    contents: write
    pull-requests: write

env:
    PREZERO_NPM_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    GITHUB_TOKEN: ${{ secrets.PREZERO_GITHUB_TOKEN }}
    COMPOSER_AUTH: '{"github-oauth":{"github.com":"${{ secrets.PREZERO_GITHUB_TOKEN }}"}}'

jobs:
    release-please:
        runs-on: prezero-github-runner
        outputs:
            release_created: ${{ steps.release.outputs.release_created }}
            tag_name: ${{ steps.release.outputs.tag_name }}
        steps:
            -   uses: googleapis/release-please-action@a02a34c4d625f9be7cb89156071d8567266a2445 # v4
                id: release
                with:
                    token: ${{ secrets.PREZERO_GITHUB_TOKEN }}

    build-release-images:
        name: Build release images
        needs: [ release-please ]
        if: ${{ needs.release-please.outputs.release_created == 'true' }}
        uses: ./.github/workflows/build-release-images.yml
        with:
            release_created: ${{ needs.release-please.outputs.release_created == 'true' }}
            tag: ${{ needs.release-please.outputs.tag_name }}
        secrets: inherit
