{"name": "prezero/hermes-iam", "description": "Hermes IAM API", "type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*", "liip/monitor-bundle": "^2.24", "prezero/api-bundle": "^1.12", "symfony/asset": "7.3.*", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2.8.1", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "7.3.*", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "7.3.*", "symfony/security-bundle": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/uid": "7.3.*", "symfony/yaml": "7.3.*", "web-token/jwt-library": "^4.0.4"}, "config": {"allow-plugins": {"captainhook/hook-installer": true, "php-http/discovery": true, "phpstan/extension-installer": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-mbstring": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*", "symfony/polyfill-php83": "*", "symfony/polyfill-php84": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}, "repositories": [{"name": "prezero/api-bundle", "type": "vcs", "url": "https://github.com/prezero/api-bundle"}], "require-dev": {"captainhook/captainhook": "^5.25.6", "captainhook/hook-installer": "^1.0.4", "codeception/codeception": "^5.3.2", "codeception/module-asserts": "^3.2", "codeception/module-phpbrowser": "^3.0.1", "codeception/module-rest": "^3.4.1", "friendsofphp/php-cs-fixer": "^3.84.0", "league/openapi-psr7-validator": "^0.22.0", "phpstan/extension-installer": "^1.4.3", "phpstan/phpstan": "^2.1.19", "phpstan/phpstan-symfony": "^2.0.7", "ramsey/conventional-commits": "^1.6", "symfony/stopwatch": "7.3.*", "symfony/web-profiler-bundle": "7.3.*"}}