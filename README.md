# Hermes IAM API

## Local Usage

- Clone the repo

```bash
git clone https://github.com/prezero/hermes-iam.git
```

- Start the docker environment

```bash
docker compose up -d --build
```

- Connect the projects to keycloak

Keycloak should be accessible on tour local machine at http://localhost:11000.

Default credentials are `admin:admin`.

To point other dockerized projects to it, make sure they connect to the host `host.docker.internal` on port 11000.
On linux the docker container would not have automatic access to `host.docker.internal`, so you would need add the 
following to the compose file to activate it:

```yaml
services:
    some-application:
        # application configuration
        extra_hosts: [ 'host.docker.internal:host-gateway' ]
```

Inside the container you should be able to access keycloak at `http://host.docker.internal:11000`.

### Updating Keycloak configuration

Do not update keycloak configuration directly in the keycloak admin console. Instead, update the `docker/local-keycloak-config/realm.json` file and restart the `keycloak-configuration` container.
Like this:

```bash
docker compose restart keycloak-configuration
```

This way the configuration will be persisted and can be shared with other developers.

**TIP** You can still use the keycloak admin console to inspect the configuration and see what changes you need to make to the `realm.json` file.
If you add the change there and go to realm export, you would find the necessary JSON field to add to the `realm.json` file.

***WARNING*** DO NOT add the full bloated export to the `realm.json` file. It will contain a lot of default configuration that we do not want to override.
Add only the changes you made. Probably a single field or block.
